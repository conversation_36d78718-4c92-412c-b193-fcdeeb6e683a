<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance.Material" />
    <style name="Base.TextAppearance.AppCompat.Body1" parent="@android:style/TextAppearance.Material.Body1" />
    <style name="Base.TextAppearance.AppCompat.Body2" parent="@android:style/TextAppearance.Material.Body2" />
    <style name="Base.TextAppearance.AppCompat.Button" parent="@android:style/TextAppearance.Material.Button" />
    <style name="Base.TextAppearance.AppCompat.Caption" parent="@android:style/TextAppearance.Material.Caption" />
    <style name="Base.TextAppearance.AppCompat.Display1" parent="@android:style/TextAppearance.Material.Display1" />
    <style name="Base.TextAppearance.AppCompat.Display2" parent="@android:style/TextAppearance.Material.Display2" />
    <style name="Base.TextAppearance.AppCompat.Display3" parent="@android:style/TextAppearance.Material.Display3" />
    <style name="Base.TextAppearance.AppCompat.Display4" parent="@android:style/TextAppearance.Material.Display4" />
    <style name="Base.TextAppearance.AppCompat.Headline" parent="@android:style/TextAppearance.Material.Headline" />
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="@android:style/TextAppearance.Material.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Large" parent="@android:style/TextAppearance.Material.Large" />
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@android:style/TextAppearance.Material.Large.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large" />
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small" />
    <style name="Base.TextAppearance.AppCompat.Medium" parent="@android:style/TextAppearance.Material.Medium" />
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@android:style/TextAppearance.Material.Medium.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Menu" parent="@android:style/TextAppearance.Material.Menu" />
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@android:style/TextAppearance.Material.SearchResult.Subtitle" />
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@android:style/TextAppearance.Material.SearchResult.Title" />
    <style name="Base.TextAppearance.AppCompat.Small" parent="@android:style/TextAppearance.Material.Small" />
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@android:style/TextAppearance.Material.Small.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="@android:style/TextAppearance.Material.Subhead" />
    <style name="Base.TextAppearance.AppCompat.Title" parent="@android:style/TextAppearance.Material.Title" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Menu" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Subtitle" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Title" />
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large" />
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small" />
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@android:style/TextAppearance.Material.Button" />
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@android:style/TextAppearance.Material.Widget.TextView.SpinnerItem" />
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle" />
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title" />
    <style name="Base.Theme.AppCompat" parent="@style/Base.V21.Theme.AppCompat" />
    <style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V21.Theme.AppCompat.Dialog" />
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V21.Theme.AppCompat.Light" />
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V21.Theme.AppCompat.Light.Dialog" />
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="@android:style/Widget.Material.ActionBar.TabText" />
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="@android:style/Widget.Material.ActionBar.TabView" />
    <style name="Base.Widget.AppCompat.ActionButton" parent="@android:style/Widget.Material.ActionButton" />
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@android:style/Widget.Material.ActionButton.CloseMode">
        <item name="android:minWidth">56.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@android:style/Widget.Material.ActionButton.Overflow" />
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.Material.AutoCompleteTextView" />
    <style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget.Material.Button" />
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="@android:style/Widget.Material.Button.Borderless" />
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@android:style/Widget.Material.Button.Borderless.Colored" />
    <style name="Base.Widget.AppCompat.Button.Small" parent="@android:style/Widget.Material.Button.Small" />
    <style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget.Material.ButtonBar" />
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.Material.CompoundButton.CheckBox" />
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.Material.CompoundButton.RadioButton" />
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="@android:style/Widget.Material.DropDownItem.Spinner" />
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@android:style/Widget.Material.Light.ActionBar.TabText" />
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@android:style/Widget.Material.Light.ActionBar.TabText" />
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@android:style/Widget.Material.Light.ActionBar.TabView" />
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@android:style/Widget.Material.Light.PopupMenu" />
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="@android:style/Widget.Material.ListPopupWindow" />
    <style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.Material.ListView" />
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="@android:style/Widget.Material.ListView.DropDown" />
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView" />
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@android:style/Widget.Material.PopupMenu" />
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Material.ProgressBar" />
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Material.ProgressBar.Horizontal" />
    <style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.Material.RatingBar" />
    <style name="Base.Widget.AppCompat.Spinner" parent="@android:style/Widget.Material.Spinner" />
    <style name="Base.Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@android:style/Widget.Material.Spinner">
        <item name="disableChildrenWhenDisabled">true</item>
        <item name="popupPromptView">@layout/abc_simple_dropdown_hint</item>
        <item name="spinnerMode">dropdown</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@android:style/Widget.Material.Spinner.Underlined" />
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.Material.TextView.SpinnerItem" />
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget.Material.Toolbar.Button.Navigation" />
    <style name="Platform.AppCompat" parent="@android:style/Theme.Material">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
    </style>
    <style name="Platform.AppCompat.Light" parent="@android:style/Theme.Material.Light">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Dark" parent="">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light" parent="">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
    </style>
    <style name="Base.V21.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
        <item name="actionBarDivider">?android:actionBarDivider</item>
        <item name="actionBarItemBackground">?android:actionBarItemBackground</item>
        <item name="actionBarSize">?android:actionBarSize</item>
        <item name="actionButtonStyle">?android:actionButtonStyle</item>
        <item name="actionMenuTextAppearance">?android:actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:actionMenuTextColor</item>
        <item name="actionModeBackground">?android:actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:actionModeCloseDrawable</item>
        <item name="actionModeShareDrawable">?android:actionModeShareDrawable</item>
        <item name="actionOverflowButtonStyle">?android:actionOverflowButtonStyle</item>
        <item name="autoCompleteTextViewStyle">?android:autoCompleteTextViewStyle</item>
        <item name="buttonStyle">?android:buttonStyle</item>
        <item name="buttonStyleSmall">?android:buttonStyleSmall</item>
        <item name="checkboxStyle">?android:checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:dividerHorizontal</item>
        <item name="dividerVertical">?android:dividerVertical</item>
        <item name="editTextBackground">?android:editTextBackground</item>
        <item name="editTextColor">?android:editTextColor</item>
        <item name="editTextStyle">?android:editTextStyle</item>
        <item name="homeAsUpIndicator">?android:homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:radioButtonStyle</item>
        <item name="ratingBarStyle">?android:ratingBarStyle</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Dialog" parent="@style/Base.V11.Theme.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
        <item name="actionBarDivider">?android:actionBarDivider</item>
        <item name="actionBarItemBackground">?android:actionBarItemBackground</item>
        <item name="actionBarSize">?android:actionBarSize</item>
        <item name="actionButtonStyle">?android:actionButtonStyle</item>
        <item name="actionMenuTextAppearance">?android:actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:actionMenuTextColor</item>
        <item name="actionModeBackground">?android:actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:actionModeCloseDrawable</item>
        <item name="actionModeShareDrawable">?android:actionModeShareDrawable</item>
        <item name="actionOverflowButtonStyle">?android:actionOverflowButtonStyle</item>
        <item name="autoCompleteTextViewStyle">?android:autoCompleteTextViewStyle</item>
        <item name="buttonStyle">?android:buttonStyle</item>
        <item name="buttonStyleSmall">?android:buttonStyleSmall</item>
        <item name="checkboxStyle">?android:checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:dividerHorizontal</item>
        <item name="dividerVertical">?android:dividerVertical</item>
        <item name="editTextBackground">?android:editTextBackground</item>
        <item name="editTextColor">?android:editTextColor</item>
        <item name="editTextStyle">?android:editTextStyle</item>
        <item name="homeAsUpIndicator">?android:homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:radioButtonStyle</item>
        <item name="ratingBarStyle">?android:ratingBarStyle</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light.Dialog" parent="@style/Base.V11.Theme.AppCompat.Light.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
</resources>
