package ratmil.domino;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

/**
 * Debug activity for testing and troubleshooting
 */
public class DebugActivity extends Activity {
    
    private TextView debugInfoText;
    private Button clearButton;
    private Button closeButton;
    private Button retryButton;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_debug);
        
        initializeViews();
        setupClickListeners();
        loadDebugInfo();
    }
    
    private void initializeViews() {
        debugInfoText = findViewById(R.id.debug_info_text);
        clearButton = findViewById(R.id.clear_button);
        closeButton = findViewById(R.id.close_button);
        retryButton = findViewById(R.id.retry_button);
    }
    
    private void setupClickListeners() {
        clearButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clearDebugInfo();
            }
        });
        
        closeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        
        retryButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loadDebugInfo();
            }
        });
    }
    
    private void loadDebugInfo() {
        StringBuilder debugInfo = new StringBuilder();
        
        debugInfo.append("=== DOMINO GAME DEBUG INFO ===\n\n");
        
        // App info
        debugInfo.append("App Version: ").append(getAppVersion()).append("\n");
        debugInfo.append("Package: ").append(getPackageName()).append("\n\n");
        
        // System info
        debugInfo.append("Android Version: ").append(android.os.Build.VERSION.RELEASE).append("\n");
        debugInfo.append("API Level: ").append(android.os.Build.VERSION.SDK_INT).append("\n");
        debugInfo.append("Device: ").append(android.os.Build.MANUFACTURER)
                  .append(" ").append(android.os.Build.MODEL).append("\n\n");
        
        // Memory info
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory() / 1024 / 1024;
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;
        
        debugInfo.append("Memory Info:\n");
        debugInfo.append("  Max: ").append(maxMemory).append(" MB\n");
        debugInfo.append("  Total: ").append(totalMemory).append(" MB\n");
        debugInfo.append("  Used: ").append(usedMemory).append(" MB\n");
        debugInfo.append("  Free: ").append(freeMemory).append(" MB\n\n");
        
        // Game settings
        debugInfo.append("Game Settings:\n");
        debugInfo.append("  Max Score: ").append(DominoSettingsActivity.GameSettings.getMaxScore(this)).append("\n");
        debugInfo.append("  Double Nine: ").append(DominoSettingsActivity.GameSettings.isDoubleNineSet(this)).append("\n");
        debugInfo.append("  Team Play: ").append(DominoSettingsActivity.GameSettings.isTeamPlay(this)).append("\n");
        debugInfo.append("  Speed: ").append(getSpeedText()).append("\n");
        debugInfo.append("  Theme: ").append(getThemeText()).append("\n\n");
        
        // Test game logic
        debugInfo.append("Game Logic Test:\n");
        debugInfo.append(testGameLogic());
        
        debugInfoText.setText(debugInfo.toString());
    }
    
    private void clearDebugInfo() {
        debugInfoText.setText("");
    }
    
    private String getAppVersion() {
        try {
            return getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
        } catch (Exception e) {
            return "Unknown";
        }
    }
    
    private String getSpeedText() {
        int speed = DominoSettingsActivity.GameSettings.getSpeed(this);
        switch (speed) {
            case 0: return "Slow";
            case 1: return "Normal";
            case 2: return "Fast";
            default: return "Unknown";
        }
    }
    
    private String getThemeText() {
        int theme = DominoSettingsActivity.GameSettings.getTheme(this);
        switch (theme) {
            case 0: return "Green";
            case 1: return "Blue";
            case 2: return "Red";
            case 3: return "Yellow";
            case 4: return "Dark";
            default: return "Unknown";
        }
    }
    
    private String testGameLogic() {
        StringBuilder test = new StringBuilder();
        
        try {
            // Test domino piece creation
            test.append("  Creating domino pieces... ");
            ratmil.domino.model.DominoPiece[] pieces = ratmil.domino.model.DominoPiece.createStandardSet();
            test.append("OK (").append(pieces.length).append(" pieces)\n");
            
            // Test game creation
            test.append("  Creating game... ");
            ratmil.domino.model.DominoGame game = new ratmil.domino.model.DominoGame(
                ratmil.domino.model.DominoGame.GameMode.SINGLE_PLAYER);
            test.append("OK\n");
            
            // Test player creation
            test.append("  Adding players... ");
            game.addPlayer(new ratmil.domino.model.Player("Test Player", 
                ratmil.domino.model.Player.PlayerType.HUMAN));
            game.addPlayer(new ratmil.domino.model.Player("AI Player", 
                ratmil.domino.model.Player.PlayerType.AI_EASY));
            test.append("OK (").append(game.getPlayers().size()).append(" players)\n");
            
            // Test AI
            test.append("  Testing AI... ");
            ratmil.domino.ai.DominoAI ai = new ratmil.domino.ai.DominoAI();
            test.append("OK\n");
            
            test.append("  All tests passed!\n");
            
        } catch (Exception e) {
            test.append("ERROR: ").append(e.getMessage()).append("\n");
            test.append("Stack trace:\n");
            for (StackTraceElement element : e.getStackTrace()) {
                test.append("  ").append(element.toString()).append("\n");
            }
        }
        
        return test.toString();
    }
}
