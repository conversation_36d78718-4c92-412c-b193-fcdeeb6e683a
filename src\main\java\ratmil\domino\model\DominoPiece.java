package ratmil.domino.model;

import java.io.Serializable;

/**
 * Represents a domino piece with two sides (top and bottom)
 * Each side can have 0-6 dots (pips)
 */
public class DominoPiece implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private final int topValue;
    private final int bottomValue;
    private boolean isPlayed;
    private boolean isFlipped;
    
    public DominoPiece(int topValue, int bottomValue) {
        this.topValue = Math.min(topValue, bottomValue);
        this.bottomValue = Math.max(topValue, bottomValue);
        this.isPlayed = false;
        this.isFlipped = false;
    }
    
    public int getTopValue() {
        return isFlipped ? bottomValue : topValue;
    }
    
    public int getBottomValue() {
        return isFlipped ? topValue : bottomValue;
    }
    
    public int getOriginalTopValue() {
        return topValue;
    }
    
    public int getOriginalBottomValue() {
        return bottomValue;
    }
    
    public boolean isDouble() {
        return topValue == bottomValue;
    }
    
    public boolean isPlayed() {
        return isPlayed;
    }
    
    public void setPlayed(boolean played) {
        this.isPlayed = played;
    }
    
    public boolean isFlipped() {
        return isFlipped;
    }
    
    public void flip() {
        this.isFlipped = !this.isFlipped;
    }
    
    public int getTotalValue() {
        return topValue + bottomValue;
    }
    
    public boolean canConnectTo(int value) {
        return topValue == value || bottomValue == value;
    }
    
    public int getOtherValue(int value) {
        if (topValue == value) {
            return bottomValue;
        } else if (bottomValue == value) {
            return topValue;
        }
        return -1; // Invalid connection
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        DominoPiece that = (DominoPiece) obj;
        return topValue == that.topValue && bottomValue == that.bottomValue;
    }
    
    @Override
    public int hashCode() {
        return topValue * 7 + bottomValue;
    }
    
    @Override
    public String toString() {
        return "[" + getTopValue() + "|" + getBottomValue() + "]";
    }
    
    /**
     * Creates a standard set of 28 domino pieces (double-six set)
     */
    public static DominoPiece[] createStandardSet() {
        DominoPiece[] pieces = new DominoPiece[28];
        int index = 0;
        
        for (int i = 0; i <= 6; i++) {
            for (int j = i; j <= 6; j++) {
                pieces[index++] = new DominoPiece(i, j);
            }
        }
        
        return pieces;
    }
    
    /**
     * Creates a double-nine set (55 pieces)
     */
    public static DominoPiece[] createDoubleNineSet() {
        DominoPiece[] pieces = new DominoPiece[55];
        int index = 0;
        
        for (int i = 0; i <= 9; i++) {
            for (int j = i; j <= 9; j++) {
                pieces[index++] = new DominoPiece(i, j);
            }
        }
        
        return pieces;
    }
}
