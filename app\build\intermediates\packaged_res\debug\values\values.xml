<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr format="reference" name="actionBarDivider"/>
    <attr format="reference" name="actionBarItemBackground"/>
    <attr format="reference" name="actionBarPopupTheme"/>
    <attr format="dimension" name="actionBarSize">
        <enum name="wrap_content" value="0"/>
    </attr>
    <attr format="reference" name="actionBarSplitStyle"/>
    <attr format="reference" name="actionBarStyle"/>
    <attr format="reference" name="actionBarTabBarStyle"/>
    <attr format="reference" name="actionBarTabStyle"/>
    <attr format="reference" name="actionBarTabTextStyle"/>
    <attr format="reference" name="actionBarTheme"/>
    <attr format="reference" name="actionBarWidgetTheme"/>
    <attr format="reference" name="actionButtonStyle"/>
    <attr format="reference" name="actionDropDownStyle"/>
    <attr format="reference" name="actionLayout"/>
    <attr format="reference" name="actionMenuTextAppearance"/>
    <attr format="reference|color" name="actionMenuTextColor"/>
    <attr format="reference" name="actionModeBackground"/>
    <attr format="reference" name="actionModeCloseButtonStyle"/>
    <attr format="reference" name="actionModeCloseDrawable"/>
    <attr format="reference" name="actionModeCopyDrawable"/>
    <attr format="reference" name="actionModeCutDrawable"/>
    <attr format="reference" name="actionModeFindDrawable"/>
    <attr format="reference" name="actionModePasteDrawable"/>
    <attr format="reference" name="actionModePopupWindowStyle"/>
    <attr format="reference" name="actionModeSelectAllDrawable"/>
    <attr format="reference" name="actionModeShareDrawable"/>
    <attr format="reference" name="actionModeSplitBackground"/>
    <attr format="reference" name="actionModeStyle"/>
    <attr format="reference" name="actionModeWebSearchDrawable"/>
    <attr format="reference" name="actionOverflowButtonStyle"/>
    <attr format="reference" name="actionOverflowMenuStyle"/>
    <attr format="string" name="actionProviderClass"/>
    <attr format="string" name="actionViewClass"/>
    <attr format="reference" name="activityChooserViewStyle"/>
    <attr format="reference" name="alertDialogButtonGroupStyle"/>
    <attr format="boolean" name="alertDialogCenterButtons"/>
    <attr format="reference" name="alertDialogStyle"/>
    <attr format="reference" name="alertDialogTheme"/>
    <attr format="reference" name="autoCompleteTextViewStyle"/>
    <attr format="reference" name="background"/>
    <attr format="reference|color" name="backgroundSplit"/>
    <attr format="reference|color" name="backgroundStacked"/>
    <attr format="color" name="backgroundTint"/>
    <attr name="backgroundTintMode">
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr>
    <attr format="dimension" name="barSize"/>
    <attr format="reference" name="buttonBarButtonStyle"/>
    <attr format="reference" name="buttonBarNegativeButtonStyle"/>
    <attr format="reference" name="buttonBarNeutralButtonStyle"/>
    <attr format="reference" name="buttonBarPositiveButtonStyle"/>
    <attr format="reference" name="buttonBarStyle"/>
    <attr format="reference" name="buttonPanelSideLayout"/>
    <attr format="reference" name="buttonStyle"/>
    <attr format="reference" name="buttonStyleSmall"/>
    <attr format="reference" name="checkboxStyle"/>
    <attr format="reference" name="checkedTextViewStyle"/>
    <attr format="reference" name="closeIcon"/>
    <attr format="reference" name="closeItemLayout"/>
    <attr format="string" name="collapseContentDescription"/>
    <attr format="reference" name="collapseIcon"/>
    <attr format="color" name="color"/>
    <attr format="color" name="colorAccent"/>
    <attr format="color" name="colorButtonNormal"/>
    <attr format="color" name="colorControlActivated"/>
    <attr format="color" name="colorControlHighlight"/>
    <attr format="color" name="colorControlNormal"/>
    <attr format="color" name="colorPrimary"/>
    <attr format="color" name="colorPrimaryDark"/>
    <attr format="color" name="colorSwitchThumbNormal"/>
    <attr format="reference" name="commitIcon"/>
    <attr format="dimension" name="contentInsetEnd"/>
    <attr format="dimension" name="contentInsetLeft"/>
    <attr format="dimension" name="contentInsetRight"/>
    <attr format="dimension" name="contentInsetStart"/>
    <attr format="reference" name="customNavigationLayout"/>
    <attr format="dimension" name="dialogPreferredPadding"/>
    <attr format="reference" name="dialogTheme"/>
    <attr format="boolean" name="disableChildrenWhenDisabled"/>
    <attr name="displayOptions">
        <flag name="disableHome" value="0x00000020"/>
        <flag name="homeAsUp" value="0x00000004"/>
        <flag name="none" value="0x00000000"/>
        <flag name="showCustom" value="0x00000010"/>
        <flag name="showHome" value="0x00000002"/>
        <flag name="showTitle" value="0x00000008"/>
        <flag name="useLogo" value="0x00000001"/>
    </attr>
    <attr format="reference" name="divider"/>
    <attr format="reference" name="dividerHorizontal"/>
    <attr format="dimension" name="dividerPadding"/>
    <attr format="reference" name="dividerVertical"/>
    <attr format="dimension" name="drawableSize"/>
    <attr format="reference" name="drawerArrowStyle"/>
    <attr format="reference" name="dropDownListViewStyle"/>
    <attr format="dimension" name="dropdownListPreferredItemHeight"/>
    <attr format="reference" name="editTextBackground"/>
    <attr format="reference|color" name="editTextColor"/>
    <attr format="reference" name="editTextStyle"/>
    <attr format="dimension" name="elevation"/>
    <attr format="reference" name="expandActivityOverflowButtonDrawable"/>
    <attr format="dimension" name="gapBetweenBars"/>
    <attr format="reference" name="goIcon"/>
    <attr format="dimension" name="height"/>
    <attr format="boolean" name="hideOnContentScroll"/>
    <attr format="reference" name="homeAsUpIndicator"/>
    <attr format="reference" name="homeLayout"/>
    <attr format="reference" name="icon"/>
    <attr format="boolean" name="iconifiedByDefault"/>
    <attr format="reference" name="indeterminateProgressStyle"/>
    <attr format="string" name="initialActivityCount"/>
    <attr format="boolean" name="isLightTheme"/>
    <attr format="dimension" name="itemPadding"/>
    <attr format="reference" name="layout"/>
    <attr format="reference" name="listChoiceBackgroundIndicator"/>
    <attr format="reference" name="listDividerAlertDialog"/>
    <attr format="reference" name="listItemLayout"/>
    <attr format="reference" name="listLayout"/>
    <attr format="reference" name="listPopupWindowStyle"/>
    <attr format="dimension" name="listPreferredItemHeight"/>
    <attr format="dimension" name="listPreferredItemHeightLarge"/>
    <attr format="dimension" name="listPreferredItemHeightSmall"/>
    <attr format="dimension" name="listPreferredItemPaddingLeft"/>
    <attr format="dimension" name="listPreferredItemPaddingRight"/>
    <attr format="reference" name="logo"/>
    <attr format="dimension" name="maxButtonHeight"/>
    <attr format="boolean" name="measureWithLargestChild"/>
    <attr format="dimension" name="middleBarArrowSize"/>
    <attr format="reference" name="multiChoiceItemLayout"/>
    <attr format="string" name="navigationContentDescription"/>
    <attr format="reference" name="navigationIcon"/>
    <attr name="navigationMode">
        <enum name="listMode" value="1"/>
        <enum name="normal" value="0"/>
        <enum name="tabMode" value="2"/>
    </attr>
    <attr format="boolean" name="overlapAnchor"/>
    <attr format="dimension" name="paddingEnd"/>
    <attr format="dimension" name="paddingStart"/>
    <attr format="reference" name="panelBackground"/>
    <attr format="reference" name="panelMenuListTheme"/>
    <attr format="dimension" name="panelMenuListWidth"/>
    <attr format="reference" name="popupMenuStyle"/>
    <attr format="reference" name="popupPromptView"/>
    <attr format="reference" name="popupTheme"/>
    <attr format="reference" name="popupWindowStyle"/>
    <attr format="boolean" name="preserveIconSpacing"/>
    <attr format="dimension" name="progressBarPadding"/>
    <attr format="reference" name="progressBarStyle"/>
    <attr format="reference" name="prompt"/>
    <attr format="reference" name="queryBackground"/>
    <attr format="string" name="queryHint"/>
    <attr format="reference" name="radioButtonStyle"/>
    <attr format="reference" name="ratingBarStyle"/>
    <attr format="reference" name="searchHintIcon"/>
    <attr format="reference" name="searchIcon"/>
    <attr format="reference" name="searchViewStyle"/>
    <attr format="reference" name="selectableItemBackground"/>
    <attr format="reference" name="selectableItemBackgroundBorderless"/>
    <attr name="showAsAction">
        <flag name="always" value="0x00000002"/>
        <flag name="collapseActionView" value="0x00000008"/>
        <flag name="ifRoom" value="0x00000001"/>
        <flag name="never" value="0x00000000"/>
        <flag name="withText" value="0x00000004"/>
    </attr>
    <attr name="showDividers">
        <flag name="beginning" value="0x00000001"/>
        <flag name="end" value="0x00000004"/>
        <flag name="middle" value="0x00000002"/>
        <flag name="none" value="0x00000000"/>
    </attr>
    <attr format="boolean" name="showText"/>
    <attr format="reference" name="singleChoiceItemLayout"/>
    <attr format="boolean" name="spinBars"/>
    <attr format="reference" name="spinnerDropDownItemStyle"/>
    <attr name="spinnerMode">
        <enum name="dialog" value="0"/>
        <enum name="dropdown" value="1"/>
    </attr>
    <attr format="reference" name="spinnerStyle"/>
    <attr format="boolean" name="splitTrack"/>
    <attr format="boolean" name="state_above_anchor"/>
    <attr format="reference" name="submitBackground"/>
    <attr format="string" name="subtitle"/>
    <attr format="reference" name="subtitleTextAppearance"/>
    <attr format="reference" name="subtitleTextStyle"/>
    <attr format="reference" name="suggestionRowLayout"/>
    <attr format="dimension" name="switchMinWidth"/>
    <attr format="dimension" name="switchPadding"/>
    <attr format="reference" name="switchStyle"/>
    <attr format="reference" name="switchTextAppearance"/>
    <attr format="reference|boolean" name="textAllCaps"/>
    <attr format="reference" name="textAppearanceLargePopupMenu"/>
    <attr format="reference" name="textAppearanceListItem"/>
    <attr format="reference" name="textAppearanceListItemSmall"/>
    <attr format="reference" name="textAppearanceSearchResultSubtitle"/>
    <attr format="reference" name="textAppearanceSearchResultTitle"/>
    <attr format="reference" name="textAppearanceSmallPopupMenu"/>
    <attr format="reference|color" name="textColorAlertDialogListItem"/>
    <attr format="reference|color" name="textColorSearchUrl"/>
    <attr format="reference" name="theme"/>
    <attr format="dimension" name="thickness"/>
    <attr format="dimension" name="thumbTextPadding"/>
    <attr format="string" name="title"/>
    <attr format="dimension" name="titleMarginBottom"/>
    <attr format="dimension" name="titleMarginEnd"/>
    <attr format="dimension" name="titleMarginStart"/>
    <attr format="dimension" name="titleMarginTop"/>
    <attr format="dimension" name="titleMargins"/>
    <attr format="reference" name="titleTextAppearance"/>
    <attr format="reference" name="titleTextStyle"/>
    <attr format="reference" name="toolbarNavigationButtonStyle"/>
    <attr format="reference" name="toolbarStyle"/>
    <attr format="dimension" name="topBottomBarArrowSize"/>
    <attr format="reference" name="track"/>
    <attr format="reference" name="voiceIcon"/>
    <attr format="boolean" name="windowActionBar"/>
    <attr format="boolean" name="windowActionBarOverlay"/>
    <attr format="boolean" name="windowActionModeOverlay"/>
    <attr format="dimension|fraction" name="windowFixedHeightMajor"/>
    <attr format="dimension|fraction" name="windowFixedHeightMinor"/>
    <attr format="dimension|fraction" name="windowFixedWidthMajor"/>
    <attr format="dimension|fraction" name="windowFixedWidthMinor"/>
    <attr format="dimension|fraction" name="windowMinWidthMajor"/>
    <attr format="dimension|fraction" name="windowMinWidthMinor"/>
    <attr format="boolean" name="windowNoTitle"/>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="abc_action_bar_embed_tabs_pre_jb">false</bool>
    <bool name="abc_action_bar_expanded_action_views_exclusive">true</bool>
    <bool name="abc_config_actionMenuItemAllCaps">true</bool>
    <bool name="abc_config_allowActionMenuItemTextWithIcon">false</bool>
    <bool name="abc_config_closeDialogWhenTouchOutside">true</bool>
    <bool name="abc_config_showMenuShortcutsWhenKeyboardPresent">false</bool>
    <color name="abc_input_method_navigation_guard">@android:color/black</color>
    <color name="abc_search_url_text_normal">#ff7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="background_floating_material_dark">#ff424242</color>
    <color name="background_floating_material_light">#ffeeeeee</color>
    <color name="background_material_dark">#ff303030</color>
    <color name="background_material_light">#ffeeeeee</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color>
    <color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="dim_foreground_disabled_material_dark">#80bebebe</color>
    <color name="dim_foreground_disabled_material_light">#80323232</color>
    <color name="dim_foreground_material_dark">#ffbebebe</color>
    <color name="dim_foreground_material_light">#ff323232</color>
    <color name="domino_accent">#ffff6f00</color>
    <color name="domino_background">#fff5f5f5</color>
    <color name="domino_button_negative">#fff44336</color>
    <color name="domino_button_neutral">#ff2196f3</color>
    <color name="domino_button_positive">#ff4caf50</color>
    <color name="domino_connection_error">#ffef5350</color>
    <color name="domino_connection_success">#ff66bb6a</color>
    <color name="domino_game_board">#ff8bc34a</color>
    <color name="domino_piece_black">#ff424242</color>
    <color name="domino_piece_white">#fffafafa</color>
    <color name="domino_player_highlight">#ffffe0b2</color>
    <color name="domino_primary">#ff2e7d32</color>
    <color name="domino_primary_dark">#ff1b5e20</color>
    <color name="domino_score_background">#ffe8f5e8</color>
    <color name="domino_surface">#ffffffff</color>
    <color name="domino_text_primary">#ff212121</color>
    <color name="domino_text_secondary">#ff757575</color>
    <color name="domino_waiting">#ffffb74d</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="hint_foreground_material_dark">@color/bright_foreground_disabled_material_dark</color>
    <color name="hint_foreground_material_light">@color/bright_foreground_disabled_material_light</color>
    <color name="link_text_material_dark">@color/material_deep_teal_200</color>
    <color name="link_text_material_light">@color/material_deep_teal_500</color>
    <color name="material_blue_grey_800">#ff37474f</color>
    <color name="material_blue_grey_900">#ff263238</color>
    <color name="material_blue_grey_950">#ff21272b</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff009688</color>
    <color name="primary_dark_material_dark">#ff000000</color>
    <color name="primary_dark_material_light">#ff757575</color>
    <color name="primary_material_dark">#ff212121</color>
    <color name="primary_material_light">#ffefefef</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="ripple_material_dark">#4dffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <dimen name="abc_action_bar_content_inset_material">16.0dip</dimen>
    <dimen name="abc_action_bar_default_height_material">56.0dip</dimen>
    <dimen name="abc_action_bar_default_padding_material">4.0dip</dimen>
    <dimen name="abc_action_bar_icon_vertical_padding_material">16.0dip</dimen>
    <dimen name="abc_action_bar_navigation_padding_start_material">0.0dip</dimen>
    <dimen name="abc_action_bar_overflow_padding_end_material">10.0dip</dimen>
    <dimen name="abc_action_bar_overflow_padding_start_material">6.0dip</dimen>
    <dimen name="abc_action_bar_progress_bar_size">40.0dip</dimen>
    <dimen name="abc_action_bar_stacked_max_height">48.0dip</dimen>
    <dimen name="abc_action_bar_stacked_tab_max_width">180.0dip</dimen>
    <dimen name="abc_action_bar_subtitle_bottom_margin_material">5.0dip</dimen>
    <dimen name="abc_action_bar_subtitle_top_margin_material">-3.0dip</dimen>
    <dimen name="abc_action_button_min_height_material">48.0dip</dimen>
    <dimen name="abc_action_button_min_width_material">48.0dip</dimen>
    <dimen name="abc_action_button_min_width_overflow_material">36.0dip</dimen>
    <dimen name="abc_alert_dialog_button_bar_height">48.0dip</dimen>
    <item name="abc_button_inset_horizontal_material" type="dimen">@dimen/abc_control_inset_material</item>
    <dimen name="abc_button_inset_vertical_material">6.0dip</dimen>
    <dimen name="abc_button_padding_horizontal_material">8.0dip</dimen>
    <item name="abc_button_padding_vertical_material" type="dimen">@dimen/abc_control_padding_material</item>
    <dimen name="abc_config_prefDialogWidth">320.0dip</dimen>
    <dimen name="abc_control_corner_material">2.0dip</dimen>
    <dimen name="abc_control_inset_material">4.0dip</dimen>
    <dimen name="abc_control_padding_material">4.0dip</dimen>
    <dimen name="abc_dialog_list_padding_vertical_material">8.0dip</dimen>
    <item name="abc_dialog_min_width_major" type="dimen">65.0%</item>
    <item name="abc_dialog_min_width_minor" type="dimen">95.00001%</item>
    <dimen name="abc_dialog_padding_material">24.0dip</dimen>
    <dimen name="abc_dialog_padding_top_material">18.0dip</dimen>
    <item name="abc_disabled_alpha_material_dark" type="dimen">0.3</item>
    <item name="abc_disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="abc_dropdownitem_icon_width">32.0dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_left">8.0dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_right">8.0dip</dimen>
    <dimen name="abc_edit_text_inset_bottom_material">7.0dip</dimen>
    <dimen name="abc_edit_text_inset_horizontal_material">4.0dip</dimen>
    <dimen name="abc_edit_text_inset_top_material">10.0dip</dimen>
    <dimen name="abc_floating_window_z">16.0dip</dimen>
    <item name="abc_list_item_padding_horizontal_material" type="dimen">@dimen/abc_action_bar_content_inset_material</item>
    <dimen name="abc_panel_menu_list_width">296.0dip</dimen>
    <dimen name="abc_search_view_preferred_width">320.0dip</dimen>
    <dimen name="abc_search_view_text_min_width">160.0dip</dimen>
    <dimen name="abc_switch_padding">3.0dip</dimen>
    <dimen name="abc_text_size_body_1_material">14.0sp</dimen>
    <dimen name="abc_text_size_body_2_material">14.0sp</dimen>
    <dimen name="abc_text_size_button_material">14.0sp</dimen>
    <dimen name="abc_text_size_caption_material">12.0sp</dimen>
    <dimen name="abc_text_size_display_1_material">34.0sp</dimen>
    <dimen name="abc_text_size_display_2_material">45.0sp</dimen>
    <dimen name="abc_text_size_display_3_material">56.0sp</dimen>
    <dimen name="abc_text_size_display_4_material">112.0sp</dimen>
    <dimen name="abc_text_size_headline_material">24.0sp</dimen>
    <dimen name="abc_text_size_large_material">22.0sp</dimen>
    <dimen name="abc_text_size_medium_material">18.0sp</dimen>
    <dimen name="abc_text_size_menu_material">16.0sp</dimen>
    <dimen name="abc_text_size_small_material">14.0sp</dimen>
    <dimen name="abc_text_size_subhead_material">16.0sp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16.0dip</dimen>
    <dimen name="abc_text_size_title_material">20.0sp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20.0dip</dimen>
    <dimen name="activity_horizontal_margin">16.0dip</dimen>
    <dimen name="activity_vertical_margin">16.0dip</dimen>
    <item name="dialog_fixed_height_major" type="dimen">79.99999%</item>
    <item name="dialog_fixed_height_minor" type="dimen">100.0%</item>
    <dimen name="dialog_fixed_width_major">320.0dip</dimen>
    <dimen name="dialog_fixed_width_minor">320.0dip</dimen>
    <item name="disabled_alpha_material_dark" type="dimen">0.3</item>
    <item name="disabled_alpha_material_light" type="dimen">0.26</item>
    <item name="action_bar" type="id"/>
    <item name="action_bar_activity_content" type="id"/>
    <item name="action_bar_container" type="id"/>
    <item name="action_bar_root" type="id"/>
    <item name="action_bar_spinner" type="id"/>
    <item name="action_bar_subtitle" type="id"/>
    <item name="action_bar_title" type="id"/>
    <item name="action_context_bar" type="id"/>
    <item name="action_menu_divider" type="id"/>
    <item name="action_menu_presenter" type="id"/>
    <item name="action_mode_bar" type="id"/>
    <item name="action_mode_bar_stub" type="id"/>
    <item name="action_mode_close_button" type="id"/>
    <item name="activity_chooser_view_content" type="id"/>
    <item name="alertTitle" type="id"/>
    <item name="buttonPanel" type="id"/>
    <item name="checkbox" type="id"/>
    <item name="contentPanel" type="id"/>
    <item name="custom" type="id"/>
    <item name="customPanel" type="id"/>
    <item name="decor_content_parent" type="id"/>
    <item name="default_activity_button" type="id"/>
    <item name="edit_query" type="id"/>
    <item name="expand_activities_button" type="id"/>
    <item name="expanded_menu" type="id"/>
    <item name="game_info" type="id"/>
    <item name="game_settings" type="id"/>
    <item name="home" type="id"/>
    <item name="icon" type="id"/>
    <item name="image" type="id"/>
    <item name="list_item" type="id"/>
    <item name="parentPanel" type="id"/>
    <item name="progress_circular" type="id"/>
    <item name="progress_horizontal" type="id"/>
    <item name="radio" type="id"/>
    <item name="scrollView" type="id"/>
    <item name="search_badge" type="id"/>
    <item name="search_bar" type="id"/>
    <item name="search_button" type="id"/>
    <item name="search_close_btn" type="id"/>
    <item name="search_edit_frame" type="id"/>
    <item name="search_go_btn" type="id"/>
    <item name="search_mag_icon" type="id"/>
    <item name="search_plate" type="id"/>
    <item name="search_src_text" type="id"/>
    <item name="search_voice_btn" type="id"/>
    <item name="select_dialog_listview" type="id"/>
    <item name="shortcut" type="id"/>
    <item name="split_action_bar" type="id"/>
    <item name="submit_area" type="id"/>
    <item name="textSpacerNoButtons" type="id"/>
    <item name="title" type="id"/>
    <item name="title_template" type="id"/>
    <item name="topPanel" type="id"/>
    <item name="up" type="id"/>
    <integer name="abc_config_activityDefaultDur">220</integer>
    <integer name="abc_config_activityShortDur">150</integer>
    <integer name="abc_max_action_buttons">2</integer>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_home_description_format">%1$s, %2$s</string>
    <string name="abc_action_bar_home_subtitle_description_format">%1$s, %2$s, %3$s</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="about_label">About</string>
    <string name="action_settings">Settings</string>
    <string name="ai1">Hal</string>
    <string name="ai2">Shirka</string>
    <string name="ai3">Eddie</string>
    <string name="app_name">Domino</string>
    <string name="blue">Blue</string>
    <string name="button_new_game">New Game</string>
    <string name="cancel">Cancel</string>
    <string name="clear_debug">Clear</string>
    <string name="close">Close</string>
    <string name="connect">Connect</string>
    <string name="connection_error">Connection error</string>
    <string name="continue_label">Continue</string>
    <string name="create_group">Create Game</string>
    <string name="creator_team">Game Creator Team</string>
    <string name="cuban_domino">Cuban Style Domino Game</string>
    <string name="debug">Debug Data</string>
    <string name="debug_info">Debug Info</string>
    <string name="demo_mode">Demo Mode</string>
    <string name="devices">Devices</string>
    <string name="different_server_version">Different Server Version</string>
    <string name="do_you_want_to_start">Do you want to start?</string>
    <string name="domino_set">Domino Set</string>
    <string name="empty_player_name">[Waiting]</string>
    <string name="error">Error</string>
    <string name="error_bluetooth_not_supported">Error. Bluetooth not supported</string>
    <string name="error_getting_game_data">Error getting game data</string>
    <string name="exit_game_msg">There is a game in progress. Do you want to quit?</string>
    <string name="exit_label">Exit</string>
    <string name="fast">Fast</string>
    <string name="game_creator">Game Creator</string>
    <string name="game_draw">Game is draw</string>
    <string name="game_info">Score</string>
    <string name="game_over">Game Over</string>
    <string name="game_setup">Game Setup</string>
    <string name="getting_game_data">Connecting</string>
    <string name="hello">Hello World, DominoActivity!</string>
    <string name="hello_world">Hello world!</string>
    <string name="i_passed">I pass</string>
    <string name="its_better_if_you_start">"It's better if you start"</string>
    <string name="join_group">Join Game</string>
    <string name="joining_game">Joining</string>
    <string name="let_me_start">Let me start</string>
    <string name="main_title">Domino</string>
    <string name="max_score">Max Score</string>
    <string name="me">Me</string>
    <string name="must_select_device">Select a device to connect</string>
    <string name="network">Network</string>
    <string name="new_game">New Game</string>
    <string name="new_game_label">New Game</string>
    <string name="next_round">Next Round</string>
    <string name="no">No</string>
    <string name="no_devices">No devices</string>
    <string name="no_let_me_start">No, let me start</string>
    <string name="no_way_you_start">No way, you start</string>
    <string name="normal">Normal</string>
    <string name="ok">OK</string>
    <string name="ok_i_will_start">"OK, I'll start"</string>
    <string name="ok_you_start">OK, you start</string>
    <string name="pale">Yellow</string>
    <string name="pieces_28">28 pieces</string>
    <string name="pieces_55">55 pieces</string>
    <string name="player_passed">Player %s passed</string>
    <string name="player_playing">Player %s is playing</string>
    <string name="player_thinking">Player %s is thinking</string>
    <string name="players">Players</string>
    <string name="ready">Touch Start when you are ready</string>
    <string name="ready_to_start">Ready to start</string>
    <string name="retry">Retry</string>
    <string name="round_over">Round Over</string>
    <string name="scanning">Scanning...</string>
    <string name="score">Score</string>
    <string name="settings">Settings</string>
    <string name="slow">Slow</string>
    <string name="speed">Speed</string>
    <string name="start">Start</string>
    <string name="start_game">Start Game</string>
    <string name="team_play">Team Play</string>
    <string name="theme">Theme</string>
    <string name="title_activity_connection">Select Device</string>
    <string name="title_activity_debug">DebugActivity</string>
    <string name="title_activity_domino">Domino</string>
    <string name="unable_to_join">Unable to join game</string>
    <string name="version">Version</string>
    <string name="waiting_for_new_round">Waiting for round to start</string>
    <string name="waiting_for_players">Waiting for players</string>
    <string name="winner_player">Winner: %s</string>
    <string name="yes">Yes</string>
    <string name="your_name">Name</string>
    <string name="your_name_title">Your Name</string>
    <string name="your_partner_doesnt_wants_to_start">"Your partner doesn't want to start"</string>
    <string name="your_partner_wants_to_start">Your partner wants to start</string>
    <style name="AlertDialog.AppCompat" parent="@style/Base.AlertDialog.AppCompat"/>
    <style name="AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat.Light"/>
    <style name="Animation.AppCompat.Dialog" parent="@style/Base.Animation.AppCompat.Dialog"/>
    <style name="Animation.AppCompat.DropDownUp" parent="@style/Base.Animation.AppCompat.DropDownUp"/>
    <style name="AppTheme" parent="@style/Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/domino_primary</item>
        <item name="colorPrimaryDark">@color/domino_primary_dark</item>
        <item name="colorAccent">@color/domino_accent</item>
        <item name="android:windowBackground">@color/domino_background</item>
        <item name="android:textColorPrimary">@color/domino_text_primary</item>
        <item name="android:textColorSecondary">@color/domino_text_secondary</item>
    </style>
    <style name="Base.AlertDialog.AppCompat" parent="@android:style/Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat"/>
    <style name="Base.Animation.AppCompat.Dialog" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?dialogPreferredPadding</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHighlight">?android:textColorHighlight</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorLink">?android:textColorLink</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textAllCaps">true</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium">
        <item name="android:textColor">?android:textColorSecondaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">18.0sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:textColorTertiary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small">
        <item name="android:textColor">?android:textColorTertiaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textColor">?android:textColorSecondaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@android:style/TextAppearance.Small">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?actionMenuTextColor</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">?android:textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@style/TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@android:style/TextAppearance.Medium">
        <item name="android:textColor">?android:textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="Base.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat"/>
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V11.Theme.AppCompat.Dialog"/>
    <style name="Base.Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowFixedHeightMajor">@dimen/dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="@style/Theme.AppCompat"/>
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light"/>
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V11.Theme.AppCompat.Light.Dialog"/>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Theme.AppCompat.Light"/>
    <style name="Base.ThemeOverlay.AppCompat" parent=""/>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="colorControlNormal">?android:textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="android:colorForeground">@color/bright_foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/hint_foreground_material_light</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/hint_foreground_material_dark</item>
        <item name="android:textColorLink">@color/link_text_material_dark</item>
        <item name="android:colorForegroundInverse">@color/bright_foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
        <item name="colorControlNormal">?android:textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:colorForeground">@color/bright_foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/hint_foreground_material_dark</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/hint_foreground_material_light</item>
        <item name="android:textColorLink">@color/link_text_material_light</item>
        <item name="android:colorForegroundInverse">@color/bright_foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.V11.Theme.AppCompat.Dialog" parent="@style/Base.V7.Theme.AppCompat.Dialog">
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">@bool/abc_config_closeDialogWhenTouchOutside</item>
    </style>
    <style name="Base.V11.Theme.AppCompat.Light.Dialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">@bool/abc_config_closeDialogWhenTouchOutside</item>
    </style>
    <style name="Base.V7.Theme.AppCompat" parent="@style/Platform.AppCompat">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Button</item>
        <item name="actionBarDivider">?dividerVertical</item>
        <item name="actionBarItemBackground">?selectableItemBackgroundBorderless</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@null</item>
        <item name="actionModeCopyDrawable">@null</item>
        <item name="actionModeCutDrawable">@null</item>
        <item name="actionModePasteDrawable">@null</item>
        <item name="actionModeSelectAllDrawable">@null</item>
        <item name="actionModeShareDrawable">@null</item>
        <item name="actionModeSplitBackground">?colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogTheme">@style/Theme.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlActivated">?colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/Theme.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@null</item>
        <item name="dividerVertical">@null</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="dropdownListPreferredItemHeight">?listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@null</item>
        <item name="isLightTheme">false</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64.0dip</item>
        <item name="listPreferredItemHeightLarge">80.0dip</item>
        <item name="listPreferredItemHeightSmall">48.0dip</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@null</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowFixedHeightMajor">0.0dip</item>
        <item name="windowFixedHeightMinor">0.0dip</item>
        <item name="windowFixedWidthMajor">0.0dip</item>
        <item name="windowFixedWidthMinor">0.0dip</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat">
        <item name="android:colorBackground">@color/background_floating_material_dark</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background_dark</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/Base.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="@style/Platform.AppCompat.Light">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Button</item>
        <item name="actionBarDivider">?dividerVertical</item>
        <item name="actionBarItemBackground">?selectableItemBackgroundBorderless</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@null</item>
        <item name="actionModeCopyDrawable">@null</item>
        <item name="actionModeCutDrawable">@null</item>
        <item name="actionModePasteDrawable">@null</item>
        <item name="actionModeSelectAllDrawable">@null</item>
        <item name="actionModeShareDrawable">@null</item>
        <item name="actionModeSplitBackground">?colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogTheme">@style/Theme.AppCompat.Light.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlActivated">?colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/Theme.AppCompat.Light.Dialog</item>
        <item name="dividerHorizontal">@null</item>
        <item name="dividerVertical">@null</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="dropdownListPreferredItemHeight">?listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@null</item>
        <item name="isLightTheme">true</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64.0dip</item>
        <item name="listPreferredItemHeightLarge">80.0dip</item>
        <item name="listPreferredItemHeightSmall">48.0dip</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@null</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowFixedHeightMajor">0.0dip</item>
        <item name="windowFixedHeightMinor">0.0dip</item>
        <item name="windowFixedWidthMajor">0.0dip</item>
        <item name="windowFixedWidthMinor">0.0dip</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">@color/background_floating_material_light</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background_light</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/Base.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="android:gravity">center_vertical</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="background">@null</item>
        <item name="backgroundSplit">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="displayOptions">showTitle</item>
        <item name="divider">?dividerVertical</item>
        <item name="elevation">8.0dip</item>
        <item name="height">?actionBarSize</item>
        <item name="popupTheme">?actionBarPopupTheme</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="background">?colorPrimary</item>
        <item name="backgroundSplit">?colorPrimary</item>
        <item name="backgroundStacked">?colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?actionBarDivider</item>
        <item name="dividerPadding">8.0dip</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:maxWidth">180.0dip</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:gravity">center_horizontal</item>
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:paddingLeft">16.0dip</item>
        <item name="android:paddingRight">16.0dip</item>
        <item name="android:layout_width">0.0dip</item>
        <item name="android:minWidth">80.0dip</item>
        <item name="android:layout_weight">1.0</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">?actionBarItemBackground</item>
        <item name="android:paddingLeft">12.0dip</item>
        <item name="android:paddingRight">12.0dip</item>
        <item name="android:scaleType">center</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:background">?selectableItemBackgroundBorderless</item>
        <item name="android:minWidth">56.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:background">?actionBarItemBackground</item>
        <item name="android:src">@null</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?actionModeBackground</item>
        <item name="backgroundSplit">?actionModeSplitBackground</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
        <item name="height">?actionBarSize</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@null</item>
        <item name="divider">?dividerVertical</item>
        <item name="dividerPadding">6.0dip</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.AutoCompleteTextView">
        <item name="android:textAppearance">?android:textAppearanceMediumInverse</item>
        <item name="android:textColor">?editTextColor</item>
        <item name="android:background">?editTextBackground</item>
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget">
        <item name="android:textAppearance">?android:textAppearanceButton</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:minWidth">88.0dip</item>
        <item name="android:minHeight">48.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless">
        <item name="android:textColor">?colorAccent</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64.0dip</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
        <item name="android:maxLines">2</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:minWidth">48.0dip</item>
        <item name="android:minHeight">48.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">?selectableItemBackgroundBorderless</item>
        <item name="android:button">?android:listChoiceIndicatorMultiple</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.CompoundButton.RadioButton">
        <item name="android:background">?selectableItemBackgroundBorderless</item>
        <item name="android:button">?android:listChoiceIndicatorSingle</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="@android:style/Widget.CompoundButton">
        <item name="android:background">?selectableItemBackgroundBorderless</item>
        <item name="android:padding">@dimen/abc_switch_padding</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="showText">false</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="track">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="color">?android:textColorSecondary</item>
        <item name="middleBarArrowSize">16.0dip</item>
        <item name="spinBars">true</item>
        <item name="thickness">2.0dip</item>
        <item name="topBottomBarArrowSize">11.309998dip</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">8.0dip</item>
        <item name="android:paddingRight">8.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="@android:style/Widget.EditText">
        <item name="android:textAppearance">?android:textAppearanceMediumInverse</item>
        <item name="android:textColor">?editTextColor</item>
        <item name="android:background">?editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
        <item name="background">?colorPrimary</item>
        <item name="backgroundSplit">?colorPrimary</item>
        <item name="backgroundStacked">?colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar"/>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText"/>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow"/>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@null</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:dropDownHorizontalOffset">0.0dip</item>
        <item name="android:dropDownVerticalOffset">0.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.ListView">
        <item name="android:listSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:divider">?dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView">
        <item name="android:divider">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="@android:style/Widget.ListView.Menu">
        <item name="android:listSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:divider">?dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow"/>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="@android:style/Widget.PopupWindow"/>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Holo.ProgressBar"/>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Holo.ProgressBar.Horizontal"/>
    <style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.RatingBar">
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_full_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_full_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="@android:style/Widget">
        <item name="closeIcon">@null</item>
        <item name="commitIcon">@null</item>
        <item name="goIcon">@null</item>
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="searchHintIcon">@null</item>
        <item name="searchIcon">@null</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
        <item name="voiceIcon">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView">
        <item name="queryBackground">@null</item>
        <item name="queryHint">@string/abc_search_hint</item>
        <item name="searchHintIcon">@null</item>
        <item name="submitBackground">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="@android:style/Widget.Holo.Spinner">
        <item name="android:background">@null</item>
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@android:style/Widget">
        <item name="android:gravity">start|center</item>
        <item name="android:background">@null</item>
        <item name="android:clickable">true</item>
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@null</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:dropDownHorizontalOffset">0.0dip</item>
        <item name="android:dropDownVerticalOffset">0.0dip</item>
        <item name="disableChildrenWhenDisabled">true</item>
        <item name="overlapAnchor">true</item>
        <item name="popupPromptView">@layout/abc_simple_dropdown_hint</item>
        <item name="spinnerMode">dropdown</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8.0dip</item>
        <item name="android:paddingRight">8.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="@android:style/Widget">
        <item name="android:minHeight">?actionBarSize</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="collapseIcon">?homeAsUpIndicator</item>
        <item name="contentInsetStart">16.0dip</item>
        <item name="maxButtonHeight">56.0dip</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="titleMargins">4.0dip</item>
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget">
        <item name="android:background">?selectableItemBackground</item>
        <item name="android:scaleType">center</item>
        <item name="android:minWidth">56.0dip</item>
    </style>
    <style name="DominoButton" parent="@style/Widget.AppCompat.Button">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">12dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:layout_margin">4dp</item>
    </style>
    <style name="DominoButton.Negative">
        <item name="android:background">@color/domino_button_negative</item>
    </style>
    <style name="DominoButton.Neutral">
        <item name="android:background">@color/domino_button_neutral</item>
    </style>
    <style name="DominoButton.Positive">
        <item name="android:background">@color/domino_button_positive</item>
    </style>
    <style name="DominoCard" parent="@android:style/Widget">
        <item name="android:background">@color/domino_surface</item>
        <item name="android:elevation">4dp</item>
        <item name="android:padding">16dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    <style name="DominoText" parent="@android:style/Widget.TextView">
        <item name="android:textColor">@color/domino_text_primary</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="DominoText.Score">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:background">@color/domino_score_background</item>
        <item name="android:padding">8dp</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="DominoText.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">16dp</item>
    </style>
    <style name="Platform.AppCompat" parent="@style/Platform.V14.AppCompat"/>
    <style name="Platform.AppCompat.Light" parent="@style/Platform.V14.AppCompat.Light"/>
    <style name="Platform.ThemeOverlay.AppCompat.Dark" parent="">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light" parent="">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
    </style>
    <style name="Platform.V11.AppCompat" parent="@android:style/Theme.Holo">
        <item name="android:colorForeground">@color/bright_foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/hint_foreground_material_light</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/hint_foreground_material_dark</item>
        <item name="android:textColorLink">@color/link_text_material_dark</item>
        <item name="android:colorForegroundInverse">@color/bright_foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLinkInverse">@color/link_text_material_light</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
    </style>
    <style name="Platform.V11.AppCompat.Light" parent="@android:style/Theme.Holo.Light">
        <item name="android:colorForeground">@color/bright_foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/hint_foreground_material_dark</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/hint_foreground_material_light</item>
        <item name="android:textColorLink">@color/link_text_material_light</item>
        <item name="android:colorForegroundInverse">@color/bright_foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLinkInverse">@color/link_text_material_dark</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
    </style>
    <style name="Platform.V12.AppCompat" parent="@style/Platform.V11.AppCompat">
        <item name="android:textCursorDrawable">@null</item>
    </style>
    <style name="Platform.V12.AppCompat.Light" parent="@style/Platform.V11.AppCompat.Light">
        <item name="android:textCursorDrawable">@null</item>
    </style>
    <style name="Platform.V14.AppCompat" parent="@style/Platform.V12.AppCompat">
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
    <style name="Platform.V14.AppCompat.Light" parent="@style/Platform.V12.AppCompat.Light">
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="@android:style/Widget">
        <item name="android:layout_gravity">center|left</item>
        <item name="android:paddingRight">8.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton.Overflow">
        <item name="android:paddingLeft">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="@android:style/Widget">
        <item name="android:paddingRight">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="@android:style/Widget">
        <item name="android:layout_marginLeft">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="@android:style/Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="@android:style/Widget">
        <item name="android:paddingLeft">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingRight">4.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="@android:style/Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="@android:style/Widget">
        <item name="android:layout_toLeftOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="@android:style/Widget">
        <item name="android:layout_alignParentRight">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="@style/Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toLeftOf">@android:id/icon2</item>
        <item name="android:layout_toRightOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="@android:style/Widget">
        <item name="android:layout_marginLeft">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/Base.Widget.AppCompat.Toolbar.Button.Navigation">
        <item name="android:paddingLeft">@dimen/abc_action_bar_navigation_padding_start_material</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="@style/Base.TextAppearance.AppCompat"/>
    <style name="TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat.Body2"/>
    <style name="TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat.Button"/>
    <style name="TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat.Display1"/>
    <style name="TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat.Display2"/>
    <style name="TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat.Display3"/>
    <style name="TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat.Display4"/>
    <style name="TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat.Headline"/>
    <style name="TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat.Inverse"/>
    <style name="TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat.Large"/>
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large.Inverse"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="@style/TextAppearance.AppCompat.SearchResult.Subtitle"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="@style/TextAppearance.AppCompat.SearchResult.Title"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat.Medium"/>
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium.Inverse"/>
    <style name="TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat.Menu"/>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Subtitle"/>
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Title"/>
    <style name="TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat.Small"/>
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small.Inverse"/>
    <style name="TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat.Subhead"/>
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead.Inverse"/>
    <style name="TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat.Title"/>
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title.Inverse"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse"/>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title"/>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Title"/>
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="@style/Base.TextAppearance.AppCompat.Widget.DropDownItem"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="@style/Base.TextAppearance.AppCompat.Widget.Switch"/>
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem"/>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item"/>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle"/>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title"/>
    <style name="Theme.AppCompat" parent="@style/Base.Theme.AppCompat"/>
    <style name="Theme.AppCompat.CompactMenu" parent="@style/Base.Theme.AppCompat.CompactMenu"/>
    <style name="Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.DialogWhenLarge"/>
    <style name="Theme.AppCompat.Light" parent="@style/Base.Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.Light.DialogWhenLarge"/>
    <style name="Theme.AppCompat.Light.NoActionBar" parent="@style/Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar" parent="@style/Theme.AppCompat">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="ThemeOverlay.AppCompat" parent="@style/Base.ThemeOverlay.AppCompat"/>
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.Dark" parent="@style/Base.ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.Light" parent="@style/Base.ThemeOverlay.AppCompat.Light"/>
    <style name="Widget.AppCompat.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar"/>
    <style name="Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar.Solid"/>
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar"/>
    <style name="Widget.AppCompat.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText"/>
    <style name="Widget.AppCompat.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView"/>
    <style name="Widget.AppCompat.ActionButton" parent="@style/Base.Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="@style/RtlOverlay.Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.ActionMode" parent="@style/Base.Widget.AppCompat.ActionMode"/>
    <style name="Widget.AppCompat.ActivityChooserView" parent="@style/Base.Widget.AppCompat.ActivityChooserView"/>
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Widget.AppCompat.Button" parent="@style/Base.Widget.AppCompat.Button"/>
    <style name="Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button.Borderless"/>
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless.Colored"/>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button.Small"/>
    <style name="Widget.AppCompat.ButtonBar" parent="@style/Base.Widget.AppCompat.ButtonBar"/>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="@style/Base.Widget.AppCompat.CompoundButton.CheckBox"/>
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="@style/Base.Widget.AppCompat.CompoundButton.RadioButton"/>
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="@style/Base.Widget.AppCompat.CompoundButton.Switch"/>
    <style name="Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text"/>
    <style name="Widget.AppCompat.EditText" parent="@style/Base.Widget.AppCompat.EditText"/>
    <style name="Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar"/>
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar.Solid"/>
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.Solid"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabBar"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabBar"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabView"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabView"/>
    <style name="Widget.AppCompat.Light.ActionButton" parent="@style/Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="@style/Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="@style/Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="@style/Widget.AppCompat.ActionMode"/>
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="@style/Widget.AppCompat.ActivityChooserView"/>
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="@style/Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="@style/Widget.AppCompat.DropDownItem.Spinner"/>
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="@style/Widget.AppCompat.ListPopupWindow"/>
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="@style/Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.Light.PopupMenu" parent="@style/Base.Widget.AppCompat.Light.PopupMenu"/>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu.Overflow"/>
    <style name="Widget.AppCompat.Light.SearchView" parent="@style/Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.ListPopupWindow" parent="@style/Base.Widget.AppCompat.ListPopupWindow"/>
    <style name="Widget.AppCompat.ListView" parent="@style/Base.Widget.AppCompat.ListView"/>
    <style name="Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView.Menu"/>
    <style name="Widget.AppCompat.PopupMenu" parent="@style/Base.Widget.AppCompat.PopupMenu"/>
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu.Overflow"/>
    <style name="Widget.AppCompat.PopupWindow" parent="@style/Base.Widget.AppCompat.PopupWindow"/>
    <style name="Widget.AppCompat.ProgressBar" parent="@style/Base.Widget.AppCompat.ProgressBar"/>
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="@style/Base.Widget.AppCompat.ProgressBar.Horizontal"/>
    <style name="Widget.AppCompat.RatingBar" parent="@style/Base.Widget.AppCompat.RatingBar"/>
    <style name="Widget.AppCompat.SearchView" parent="@style/Base.Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView.ActionBar"/>
    <style name="Widget.AppCompat.Spinner" parent="@style/Base.Widget.AppCompat.Spinner"/>
    <style name="Widget.AppCompat.Spinner.DropDown" parent="@style/Widget.AppCompat.Spinner"/>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@style/Base.Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner.Underlined"/>
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="@style/Base.Widget.AppCompat.TextView.SpinnerItem"/>
    <style name="Widget.AppCompat.Toolbar" parent="@style/Base.Widget.AppCompat.Toolbar"/>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/RtlOverlay.Widget.AppCompat.Toolbar.Button.Navigation"/>
</resources>