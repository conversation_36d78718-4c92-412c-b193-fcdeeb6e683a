<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="domino_game_board" modulePackage="ratmil.domino" filePath="app\src\main\res\layout\domino_game_board.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/domino_game_board_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="16"/></Target><Target id="@+id/game_board_scroll" view="ScrollView"><Expressions/><location startLine="8" startOffset="4" endLine="29" endOffset="16"/></Target><Target id="@+id/domino_chain" view="LinearLayout"><Expressions/><location startLine="21" startOffset="12" endLine="27" endOffset="42"/></Target><Target id="@+id/score_area" view="LinearLayout"><Expressions/><location startLine="32" startOffset="4" endLine="69" endOffset="18"/></Target><Target id="@+id/player1_score" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="49" endOffset="44"/></Target><Target id="@+id/game_status" view="TextView"><Expressions/><location startLine="51" startOffset="8" endLine="59" endOffset="51"/></Target><Target id="@+id/player2_score" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="68" endOffset="46"/></Target><Target id="@+id/player_area" view="LinearLayout"><Expressions/><location startLine="72" startOffset="4" endLine="129" endOffset="18"/></Target><Target id="@+id/player_hand" view="LinearLayout"><Expressions/><location startLine="88" startOffset="12" endLine="93" endOffset="39"/></Target><Target id="@+id/pass_button" view="Button"><Expressions/><location startLine="102" startOffset="12" endLine="109" endOffset="48"/></Target><Target id="@+id/menu_button" view="Button"><Expressions/><location startLine="111" startOffset="12" endLine="118" endOffset="55"/></Target><Target id="@+id/start_button" view="Button"><Expressions/><location startLine="120" startOffset="12" endLine="127" endOffset="50"/></Target></Targets></Layout>