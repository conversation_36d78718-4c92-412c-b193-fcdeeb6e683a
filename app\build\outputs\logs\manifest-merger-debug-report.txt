-- Merging decision tree log ---
manifest
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:1-91:12
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:1-91:12
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:1-91:12
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:1-91:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\596e41f98414496ed17ba351110798d1\transformed\viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\8679f45878a6127de1b97684ca2efa9a\transformed\material-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5481e69d8f84515ce494f399a84ad271\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3208b4076163ce1e2a7713159266ef12\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e9ae72c11e6e08c08a7e177bedd18d43\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57460903ca3e80addc8d239b664c6528\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\a26d3da4b7b4032005e4a32bcb352725\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\05200fa1caac22c9582ea74012c09a5d\transformed\activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2e5d2b000bc37a81a4e115a6069046d\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\08efa17b250fc952daef054262f4ee54\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed2751821164e3895202a3aa4a4a1a93\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6bc1c8bf5a8d0d5d3785c1e89a27653\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce0e7f47fb605c2cc48c23f129b29c2\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\291efe53dac6f8efee16989d744d47f9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ca7ef1c078d7bc4171d710dee084a64\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a87b7d3d5a1d3ec5e1d4f66de3b846a3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f024a817e33503a3ea09bfea6bdfb09\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\5fef40174ad4dd6c558553ea322b27bc\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef6fb06b539fc66cc9b37f3b843be1e3\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\792590d5bae8a1ec4831e57cf53b78a1\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f161ddd16c8ed7905caf1c7289f34438\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62eed694a81a9bf07273ffb9d97001f1\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\be07579c905a5cfe97e871a0599d8035\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57d32e08dba2ec83c27081448b9e7507\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\162096ff1284611663381f5cf2ec2ee5\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5164ffc98b40288d4bcbc7e0a769fbfd\transformed\savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c85d8bbe6023dc950e53082041a0f76\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\279fe99864f8a6540add8351e5b703a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a9a9dcff012fc94205a72816b3c1cd8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\290fcded14b5d3ae4be841eade24da51\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ffc493ae6823b9866c9c25dac783f7f\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\44b015214d0a2100eabd7c40eac2bd5d\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\304f5ada852cc7d49a3fdae56cf63df1\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6deb55fc88727588469c9874c287a988\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0f092047fada35676c015815910e49d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\74138a04cd4bff3b3b55e27e4575989c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\330500c5c8d43f8fc84d20481cf6e72a\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b48de6ddbcbfe46abe0900993a8e0088\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:1-91:12
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:1-91:12
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:1-91:12
	package
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:4:5-28
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:1-91:12
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:1-91:12
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.BLUETOOTH
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:7:5-8:38
	android:maxSdkVersion
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:8:9-35
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:7:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:9:22-71
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:13:5-15:31
	android:usesPermissionFlags
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:14:9-55
	tools:targetApi
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:15:9-28
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:13:22-70
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:16:5-17:31
	tools:targetApi
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:17:9-28
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:16:22-75
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:18:5-19:31
	tools:targetApi
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:19:9-28
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:18:22-73
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:22:5-81
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:22:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:23:5-79
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:23:22-76
uses-feature#android.hardware.bluetooth
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:26:5-88
	android:required
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:26:61-85
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:26:19-60
application
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:28:5-90:19
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\8679f45878a6127de1b97684ca2efa9a\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\8679f45878a6127de1b97684ca2efa9a\transformed\material-1.8.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5481e69d8f84515ce494f399a84ad271\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5481e69d8f84515ce494f399a84ad271\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\279fe99864f8a6540add8351e5b703a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\279fe99864f8a6540add8351e5b703a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a9a9dcff012fc94205a72816b3c1cd8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a9a9dcff012fc94205a72816b3c1cd8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:33:9-35
	android:label
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:31:9-41
	tools:targetApi
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:34:9-29
	android:icon
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:30:9-43
	android:allowBackup
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:29:9-35
	android:theme
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:32:9-40
activity#ratmil.domino.DominoMainActivity
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:37:9-46:20
	android:label
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:39:13-45
	android:exported
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:41:13-36
	android:theme
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:40:13-44
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:38:13-47
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:42:13-45:29
action#android.intent.action.MAIN
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:43:17-69
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:43:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:44:17-77
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:44:27-74
activity#ratmil.domino.DominoActivity
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:49:9-54:40
	android:screenOrientation
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:53:13-50
	android:label
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:51:13-58
	android:exported
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:54:13-37
	android:theme
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:52:13-44
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:50:13-43
activity#ratmil.domino.DominoGameSetupActivity
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:57:9-61:40
	android:label
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:59:13-47
	android:exported
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:61:13-37
	android:theme
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:60:13-44
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:58:13-52
activity#ratmil.domino.DominoSettingsActivity
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:64:9-68:40
	android:label
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:66:13-45
	android:exported
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:67:13-44
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:65:13-51
activity#ratmil.domino.DominoConnectionActivity
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:71:9-75:40
	android:label
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:73:13-62
	android:exported
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:75:13-37
	android:theme
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:74:13-44
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:72:13-53
activity#ratmil.domino.DebugActivity
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:78:9-82:40
	android:label
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:80:13-57
	android:exported
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:82:13-37
	android:theme
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:81:13-44
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:79:13-42
service#ratmil.domino.GameConnectionService
ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:85:9-88:40
	android:label
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:87:13-44
	android:exported
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:88:13-37
	android:name
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml:86:13-50
uses-sdk
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\596e41f98414496ed17ba351110798d1\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\596e41f98414496ed17ba351110798d1\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\8679f45878a6127de1b97684ca2efa9a\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\8679f45878a6127de1b97684ca2efa9a\transformed\material-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5481e69d8f84515ce494f399a84ad271\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\5481e69d8f84515ce494f399a84ad271\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3208b4076163ce1e2a7713159266ef12\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3208b4076163ce1e2a7713159266ef12\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e9ae72c11e6e08c08a7e177bedd18d43\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e9ae72c11e6e08c08a7e177bedd18d43\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57460903ca3e80addc8d239b664c6528\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57460903ca3e80addc8d239b664c6528\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\a26d3da4b7b4032005e4a32bcb352725\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\a26d3da4b7b4032005e4a32bcb352725\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\05200fa1caac22c9582ea74012c09a5d\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\05200fa1caac22c9582ea74012c09a5d\transformed\activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2e5d2b000bc37a81a4e115a6069046d\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2e5d2b000bc37a81a4e115a6069046d\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\08efa17b250fc952daef054262f4ee54\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\08efa17b250fc952daef054262f4ee54\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed2751821164e3895202a3aa4a4a1a93\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed2751821164e3895202a3aa4a4a1a93\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6bc1c8bf5a8d0d5d3785c1e89a27653\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6bc1c8bf5a8d0d5d3785c1e89a27653\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce0e7f47fb605c2cc48c23f129b29c2\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1ce0e7f47fb605c2cc48c23f129b29c2\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\291efe53dac6f8efee16989d744d47f9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\291efe53dac6f8efee16989d744d47f9\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ca7ef1c078d7bc4171d710dee084a64\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7ca7ef1c078d7bc4171d710dee084a64\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a87b7d3d5a1d3ec5e1d4f66de3b846a3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a87b7d3d5a1d3ec5e1d4f66de3b846a3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f024a817e33503a3ea09bfea6bdfb09\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f024a817e33503a3ea09bfea6bdfb09\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\5fef40174ad4dd6c558553ea322b27bc\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\5fef40174ad4dd6c558553ea322b27bc\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef6fb06b539fc66cc9b37f3b843be1e3\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\ef6fb06b539fc66cc9b37f3b843be1e3\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\792590d5bae8a1ec4831e57cf53b78a1\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\792590d5bae8a1ec4831e57cf53b78a1\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f161ddd16c8ed7905caf1c7289f34438\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f161ddd16c8ed7905caf1c7289f34438\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62eed694a81a9bf07273ffb9d97001f1\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62eed694a81a9bf07273ffb9d97001f1\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\be07579c905a5cfe97e871a0599d8035\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\be07579c905a5cfe97e871a0599d8035\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57d32e08dba2ec83c27081448b9e7507\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57d32e08dba2ec83c27081448b9e7507\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\162096ff1284611663381f5cf2ec2ee5\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\162096ff1284611663381f5cf2ec2ee5\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5164ffc98b40288d4bcbc7e0a769fbfd\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\5164ffc98b40288d4bcbc7e0a769fbfd\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c85d8bbe6023dc950e53082041a0f76\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c85d8bbe6023dc950e53082041a0f76\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\279fe99864f8a6540add8351e5b703a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\279fe99864f8a6540add8351e5b703a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a9a9dcff012fc94205a72816b3c1cd8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a9a9dcff012fc94205a72816b3c1cd8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\290fcded14b5d3ae4be841eade24da51\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\290fcded14b5d3ae4be841eade24da51\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ffc493ae6823b9866c9c25dac783f7f\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ffc493ae6823b9866c9c25dac783f7f\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\44b015214d0a2100eabd7c40eac2bd5d\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\44b015214d0a2100eabd7c40eac2bd5d\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\304f5ada852cc7d49a3fdae56cf63df1\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\304f5ada852cc7d49a3fdae56cf63df1\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6deb55fc88727588469c9874c287a988\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6deb55fc88727588469c9874c287a988\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0f092047fada35676c015815910e49d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0f092047fada35676c015815910e49d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\74138a04cd4bff3b3b55e27e4575989c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\74138a04cd4bff3b3b55e27e4575989c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\330500c5c8d43f8fc84d20481cf6e72a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\330500c5c8d43f8fc84d20481cf6e72a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b48de6ddbcbfe46abe0900993a8e0088\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b48de6ddbcbfe46abe0900993a8e0088\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
		ADDED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
		INJECTED from D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a9a9dcff012fc94205a72816b3c1cd8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5a9a9dcff012fc94205a72816b3c1cd8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\844d74b9f337614bd62ab2e9fe0ffe35\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#ratmil.domino.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#ratmil.domino.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\568daac70474b1fc782db35662e4983d\transformed\lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
