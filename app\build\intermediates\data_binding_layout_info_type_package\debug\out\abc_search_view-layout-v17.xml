<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="abc_search_view" modulePackage="ratmil.domino" filePath="app\src\main\res\layout-v17\abc_search_view.xml" directory="layout-v17" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@id/search_bar"><Targets><Target id="@id/search_bar" tag="layout-v17/abc_search_view_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="111" endOffset="14"/></Target><Target id="@id/search_badge" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="16" endOffset="42"/></Target><Target id="@id/search_button" view="android.support.v7.internal.widget.TintImageView"><Expressions/><location startLine="17" startOffset="4" endLine="24" endOffset="36"/></Target><Target id="@id/search_edit_frame" view="LinearLayout"><Expressions/><location startLine="25" startOffset="4" endLine="110" endOffset="18"/></Target><Target id="@id/search_mag_icon" view="android.support.v7.internal.widget.TintImageView"><Expressions/><location startLine="37" startOffset="8" endLine="44" endOffset="75"/></Target><Target id="@id/search_plate" view="LinearLayout"><Expressions/><location startLine="45" startOffset="8" endLine="81" endOffset="22"/></Target><Target id="@id/search_src_text" view="android.support.v7.widget.SearchView$SearchAutoComplete"><Expressions/><location startLine="52" startOffset="12" endLine="70" endOffset="81"/></Target><Target id="@id/search_close_btn" view="android.support.v7.internal.widget.TintImageView"><Expressions/><location startLine="71" startOffset="12" endLine="80" endOffset="87"/></Target><Target id="@id/submit_area" view="LinearLayout"><Expressions/><location startLine="82" startOffset="8" endLine="109" endOffset="22"/></Target><Target id="@id/search_go_btn" view="android.support.v7.internal.widget.TintImageView"><Expressions/><location startLine="87" startOffset="12" endLine="97" endOffset="88"/></Target><Target id="@id/search_voice_btn" view="android.support.v7.internal.widget.TintImageView"><Expressions/><location startLine="98" startOffset="12" endLine="108" endOffset="87"/></Target></Targets></Layout>