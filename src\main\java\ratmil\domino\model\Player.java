package ratmil.domino.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a player in the domino game
 */
public class Player implements Serializable {
    private static final long serialVersionUID = 1L;
    
    public enum PlayerType {
        HUMAN, AI_EASY, AI_MEDIUM, AI_HARD
    }
    
    private String name;
    private PlayerType type;
    private List<DominoPiece> hand;
    private int score;
    private boolean hasPassed;
    private boolean isMyTurn;
    private int roundsWon;
    
    public Player(String name, PlayerType type) {
        this.name = name;
        this.type = type;
        this.hand = new ArrayList<>();
        this.score = 0;
        this.hasPassed = false;
        this.isMyTurn = false;
        this.roundsWon = 0;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public PlayerType getType() {
        return type;
    }
    
    public List<DominoPiece> getHand() {
        return hand;
    }
    
    public void addPiece(DominoPiece piece) {
        hand.add(piece);
    }
    
    public boolean removePiece(DominoPiece piece) {
        return hand.remove(piece);
    }
    
    public int getHandSize() {
        return hand.size();
    }
    
    public boolean hasNoPieces() {
        return hand.isEmpty();
    }
    
    public int getScore() {
        return score;
    }
    
    public void addScore(int points) {
        this.score += points;
    }
    
    public void setScore(int score) {
        this.score = score;
    }
    
    public boolean hasPassed() {
        return hasPassed;
    }
    
    public void setHasPassed(boolean hasPassed) {
        this.hasPassed = hasPassed;
    }
    
    public boolean isMyTurn() {
        return isMyTurn;
    }
    
    public void setMyTurn(boolean myTurn) {
        this.isMyTurn = myTurn;
    }
    
    public int getRoundsWon() {
        return roundsWon;
    }
    
    public void incrementRoundsWon() {
        this.roundsWon++;
    }
    
    public void resetRoundsWon() {
        this.roundsWon = 0;
    }
    
    /**
     * Calculate the total value of pieces in hand
     */
    public int getHandValue() {
        int total = 0;
        for (DominoPiece piece : hand) {
            total += piece.getTotalValue();
        }
        return total;
    }
    
    /**
     * Check if player can play any piece
     */
    public boolean canPlay(int leftValue, int rightValue) {
        for (DominoPiece piece : hand) {
            if (piece.canConnectTo(leftValue) || piece.canConnectTo(rightValue)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get all playable pieces for current board state
     */
    public List<DominoPiece> getPlayablePieces(int leftValue, int rightValue) {
        List<DominoPiece> playable = new ArrayList<>();
        for (DominoPiece piece : hand) {
            if (piece.canConnectTo(leftValue) || piece.canConnectTo(rightValue)) {
                playable.add(piece);
            }
        }
        return playable;
    }
    
    /**
     * Check if player has a double piece with specific value
     */
    public boolean hasDouble(int value) {
        for (DominoPiece piece : hand) {
            if (piece.isDouble() && piece.getTopValue() == value) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get the highest double in hand
     */
    public DominoPiece getHighestDouble() {
        DominoPiece highest = null;
        for (DominoPiece piece : hand) {
            if (piece.isDouble()) {
                if (highest == null || piece.getTopValue() > highest.getTopValue()) {
                    highest = piece;
                }
            }
        }
        return highest;
    }
    
    /**
     * Reset player for new round
     */
    public void resetForNewRound() {
        hand.clear();
        hasPassed = false;
        isMyTurn = false;
    }
    
    /**
     * Reset player for new game
     */
    public void resetForNewGame() {
        resetForNewRound();
        score = 0;
        roundsWon = 0;
    }
    
    @Override
    public String toString() {
        return name + " (" + type + ") - Score: " + score + ", Hand: " + hand.size() + " pieces";
    }
}
