# 🚀 تعليمات بناء مشروع لعبة الدومينو

## 🎯 المشكلة الحالية
المشكلة في بناء المشروع تعود إلى **اسم المجلد** الذي يحتوي على أحرف خاصة `Domino_v0.4(3)_base_src` مما يسبب مشاكل في مسارات الملفات.

## ✅ الحل السريع

### 1. إنشاء مجلد جديد بمسار صحيح:
```bash
mkdir C:\DominoGame
```

### 2. نسخ جميع الملفات إلى المجلد الجديد:
```bash
# نسخ ملفات البناء
copy build.gradle C:\DominoGame\
copy settings.gradle C:\DominoGame\
copy gradle.properties C:\DominoGame\
copy local.properties C:\DominoGame\
copy gradlew.bat C:\DominoGame\

# نسخ مجلد app بالكامل
xcopy /E /I app C:\DominoGame\app

# نسخ مجلد gradle
xcopy /E /I gradle C:\DominoGame\gradle
```

### 3. بناء المشروع:
```bash
cd C:\DominoGame
gradle clean assembleDebug
```

## 📁 بنية المشروع الصحيحة

```
C:\DominoGame\
├── build.gradle
├── settings.gradle
├── gradle.properties
├── local.properties
├── gradlew.bat
├── gradle\
│   └── wrapper\
│       └── gradle-wrapper.properties
└── app\
    ├── build.gradle
    ├── proguard-rules.pro
    └── src\
        └── main\
            ├── AndroidManifest.xml
            ├── java\
            │   └── ratmil\
            │       └── domino\
            │           ├── model\
            │           ├── ai\
            │           └── *.java
            └── res\
                ├── values\
                ├── values-ar\
                ├── values-night\
                ├── layout\
                ├── drawable\
                ├── anim\
                └── menu\
```

## 🔧 إصلاح المشاكل المحتملة

### مشكلة Android SDK:
```bash
# تحديث local.properties
echo "sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk" > local.properties
```

### مشكلة Java:
```bash
# التأكد من وجود Java 8+
java -version
```

### مشكلة Gradle:
```bash
# استخدام Gradle Wrapper
gradlew.bat clean assembleDebug
```

## 🎮 تشغيل اللعبة

بعد البناء الناجح، ستجد ملف APK في:
```
app\build\outputs\apk\debug\app-debug.apk
```

## 🛠️ البناء باستخدام Android Studio

1. افتح Android Studio
2. اختر "Open an existing Android Studio project"
3. اختر مجلد `C:\DominoGame`
4. انتظر حتى يتم تحميل المشروع
5. اضغط على "Build" → "Make Project"

## 📱 تثبيت على الجهاز

```bash
# تثبيت APK على جهاز متصل
adb install app\build\outputs\apk\debug\app-debug.apk
```

## 🎯 الميزات المكتملة

✅ **الكود المصدري الكامل**
- منطق اللعبة الكوبية الأصيل
- ذكاء اصطناعي بثلاث مستويات
- دعم البلوتوث للعب الجماعي

✅ **واجهة المستخدم المتطورة**
- دعم كامل للغة العربية
- نظام ألوان حديث مع الوضع الليلي
- تصميم Material Design

✅ **الميزات المتقدمة**
- وضع اللاعب الواحد
- وضع اللعب الجماعي
- وضع العرض التوضيحي
- إعدادات شاملة

## 🚨 ملاحظات مهمة

1. **تجنب المسارات التي تحتوي على أحرف خاصة**
2. **تأكد من تثبيت Android SDK**
3. **استخدم Java 8 أو أحدث**
4. **تأكد من اتصال الإنترنت لتحميل المكتبات**

## 🎉 النتيجة النهائية

بعد البناء الناجح، ستحصل على:
- **لعبة دومينو كاملة** جاهزة للتشغيل
- **دعم كامل للغة العربية**
- **ذكاء اصطناعي متقدم**
- **واجهة مستخدم حديثة**
- **دعم البلوتوث للعب الجماعي**

---

**المشروع جاهز للاستخدام! 🎮✨**
