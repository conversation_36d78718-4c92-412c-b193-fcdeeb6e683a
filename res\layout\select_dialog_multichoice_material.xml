<?xml version="1.0" encoding="utf-8"?>
<CheckedTextView
    android:textAppearance="?android:textAppearanceMedium"
    android:textColor="?textColorAlertDialogListItem"
    android:ellipsize="marquee"
    android:gravity="center_vertical"
    android:id="@android:id/text1"
    android:paddingLeft="?dialogPreferredPadding"
    android:paddingRight="?dialogPreferredPadding"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:checkMark="?android:listChoiceIndicatorMultiple"
    android:minHeight="?listPreferredItemHeightSmall"
    xmlns:android="http://schemas.android.com/apk/res/android" />