<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/domino_background"
    android:padding="16dp">

    <!-- Game Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/cuban_domino"
        style="@style/DominoText.Title"
        android:background="@drawable/domino_card_background"
        android:layout_marginBottom="24dp" />

    <!-- Main Menu <PERSON>tons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:background="@drawable/domino_card_background"
        android:padding="24dp">

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/button_new_game"
            style="@style/DominoButton.Positive"
            android:layout_marginBottom="16dp"
            android:drawableStart="@android:drawable/ic_media_play"
            android:drawablePadding="8dp" />

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/join_group"
            style="@style/DominoButton.Neutral"
            android:layout_marginBottom="16dp"
            android:drawableStart="@android:drawable/ic_menu_share"
            android:drawablePadding="8dp" />

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/create_group"
            style="@style/DominoButton.Neutral"
            android:layout_marginBottom="16dp"
            android:drawableStart="@android:drawable/ic_menu_add"
            android:drawablePadding="8dp" />

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/demo_mode"
            style="@style/DominoButton.Neutral"
            android:layout_marginBottom="16dp"
            android:drawableStart="@android:drawable/ic_menu_view"
            android:drawablePadding="8dp" />

        <Button
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/settings"
            style="@style/DominoButton.Neutral"
            android:layout_marginBottom="16dp"
            android:drawableStart="@android:drawable/ic_menu_preferences"
            android:drawablePadding="8dp" />
    </LinearLayout>

    <!-- Footer -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/version"
            style="@style/DominoText"
            android:gravity="start" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/exit_label"
            style="@style/DominoButton.Negative"
            android:minWidth="80dp" />
    </LinearLayout>
</LinearLayout>
