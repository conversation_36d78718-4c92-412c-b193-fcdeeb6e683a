R_DEF: Internal format may change without notice
local
anim abc_fade_in
anim abc_fade_out
anim abc_grow_fade_in_from_bottom
anim abc_popup_enter
anim abc_popup_exit
anim abc_shrink_fade_out_from_bottom
anim abc_slide_in_bottom
anim abc_slide_in_top
anim abc_slide_out_bottom
anim abc_slide_out_top
anim domino_button_press
anim domino_piece_flip
anim domino_slide_in_left
anim domino_slide_in_right
attr actionBarDivider
attr actionBarItemBackground
attr actionBarPopupTheme
attr actionBarSize
attr actionBarSplitStyle
attr actionBarStyle
attr actionBarTabBarStyle
attr actionBarTabStyle
attr actionBarTabTextStyle
attr actionBarTheme
attr actionBarWidgetTheme
attr actionButtonStyle
attr actionDropDownStyle
attr actionLayout
attr actionMenuTextAppearance
attr actionMenuTextColor
attr actionModeBackground
attr actionModeCloseButtonStyle
attr actionModeCloseDrawable
attr actionModeCopyDrawable
attr actionModeCutDrawable
attr actionModeFindDrawable
attr actionModePasteDrawable
attr actionModePopupWindowStyle
attr actionModeSelectAllDrawable
attr actionModeShareDrawable
attr actionModeSplitBackground
attr actionModeStyle
attr actionModeWebSearchDrawable
attr actionOverflowButtonStyle
attr actionOverflowMenuStyle
attr actionProviderClass
attr actionViewClass
attr activityChooserViewStyle
attr alertDialogButtonGroupStyle
attr alertDialogCenterButtons
attr alertDialogStyle
attr alertDialogTheme
attr autoCompleteTextViewStyle
attr background
attr backgroundSplit
attr backgroundStacked
attr backgroundTint
attr backgroundTintMode
attr barSize
attr buttonBarButtonStyle
attr buttonBarNegativeButtonStyle
attr buttonBarNeutralButtonStyle
attr buttonBarPositiveButtonStyle
attr buttonBarStyle
attr buttonPanelSideLayout
attr buttonStyle
attr buttonStyleSmall
attr checkboxStyle
attr checkedTextViewStyle
attr closeIcon
attr closeItemLayout
attr collapseContentDescription
attr collapseIcon
attr color
attr colorAccent
attr colorButtonNormal
attr colorControlActivated
attr colorControlHighlight
attr colorControlNormal
attr colorPrimary
attr colorPrimaryDark
attr colorSwitchThumbNormal
attr commitIcon
attr contentInsetEnd
attr contentInsetLeft
attr contentInsetRight
attr contentInsetStart
attr customNavigationLayout
attr dialogPreferredPadding
attr dialogTheme
attr disableChildrenWhenDisabled
attr displayOptions
attr divider
attr dividerHorizontal
attr dividerPadding
attr dividerVertical
attr drawableSize
attr drawerArrowStyle
attr dropDownListViewStyle
attr dropdownListPreferredItemHeight
attr editTextBackground
attr editTextColor
attr editTextStyle
attr elevation
attr expandActivityOverflowButtonDrawable
attr gapBetweenBars
attr goIcon
attr height
attr hideOnContentScroll
attr homeAsUpIndicator
attr homeLayout
attr icon
attr iconifiedByDefault
attr indeterminateProgressStyle
attr initialActivityCount
attr isLightTheme
attr itemPadding
attr layout
attr listChoiceBackgroundIndicator
attr listDividerAlertDialog
attr listItemLayout
attr listLayout
attr listPopupWindowStyle
attr listPreferredItemHeight
attr listPreferredItemHeightLarge
attr listPreferredItemHeightSmall
attr listPreferredItemPaddingLeft
attr listPreferredItemPaddingRight
attr logo
attr maxButtonHeight
attr measureWithLargestChild
attr middleBarArrowSize
attr multiChoiceItemLayout
attr navigationContentDescription
attr navigationIcon
attr navigationMode
attr overlapAnchor
attr paddingEnd
attr paddingStart
attr panelBackground
attr panelMenuListTheme
attr panelMenuListWidth
attr popupMenuStyle
attr popupPromptView
attr popupTheme
attr popupWindowStyle
attr preserveIconSpacing
attr progressBarPadding
attr progressBarStyle
attr prompt
attr queryBackground
attr queryHint
attr radioButtonStyle
attr ratingBarStyle
attr searchHintIcon
attr searchIcon
attr searchViewStyle
attr selectableItemBackground
attr selectableItemBackgroundBorderless
attr showAsAction
attr showDividers
attr showText
attr singleChoiceItemLayout
attr spinBars
attr spinnerDropDownItemStyle
attr spinnerMode
attr spinnerStyle
attr splitTrack
attr state_above_anchor
attr submitBackground
attr subtitle
attr subtitleTextAppearance
attr subtitleTextStyle
attr suggestionRowLayout
attr switchMinWidth
attr switchPadding
attr switchStyle
attr switchTextAppearance
attr textAllCaps
attr textAppearanceLargePopupMenu
attr textAppearanceListItem
attr textAppearanceListItemSmall
attr textAppearanceSearchResultSubtitle
attr textAppearanceSearchResultTitle
attr textAppearanceSmallPopupMenu
attr textColorAlertDialogListItem
attr textColorSearchUrl
attr theme
attr thickness
attr thumbTextPadding
attr title
attr titleMarginBottom
attr titleMarginEnd
attr titleMarginStart
attr titleMarginTop
attr titleMargins
attr titleTextAppearance
attr titleTextStyle
attr toolbarNavigationButtonStyle
attr toolbarStyle
attr topBottomBarArrowSize
attr track
attr voiceIcon
attr windowActionBar
attr windowActionBarOverlay
attr windowActionModeOverlay
attr windowFixedHeightMajor
attr windowFixedHeightMinor
attr windowFixedWidthMajor
attr windowFixedWidthMinor
attr windowMinWidthMajor
attr windowMinWidthMinor
attr windowNoTitle
bool abc_action_bar_embed_tabs
bool abc_action_bar_embed_tabs_pre_jb
bool abc_action_bar_expanded_action_views_exclusive
bool abc_config_actionMenuItemAllCaps
bool abc_config_allowActionMenuItemTextWithIcon
bool abc_config_closeDialogWhenTouchOutside
bool abc_config_showMenuShortcutsWhenKeyboardPresent
color abc_background_cache_hint_selector_material_dark
color abc_background_cache_hint_selector_material_light
color abc_input_method_navigation_guard
color abc_primary_text_disable_only_material_dark
color abc_primary_text_disable_only_material_light
color abc_primary_text_material_dark
color abc_primary_text_material_light
color abc_search_url_text
color abc_search_url_text_normal
color abc_search_url_text_pressed
color abc_search_url_text_selected
color abc_secondary_text_material_dark
color abc_secondary_text_material_light
color accent_material_dark
color accent_material_light
color background_floating_material_dark
color background_floating_material_light
color background_material_dark
color background_material_light
color bright_foreground_disabled_material_dark
color bright_foreground_disabled_material_light
color bright_foreground_inverse_material_dark
color bright_foreground_inverse_material_light
color bright_foreground_material_dark
color bright_foreground_material_light
color button_material_dark
color button_material_light
color dim_foreground_disabled_material_dark
color dim_foreground_disabled_material_light
color dim_foreground_material_dark
color dim_foreground_material_light
color domino_accent
color domino_background
color domino_button_negative
color domino_button_neutral
color domino_button_positive
color domino_connection_error
color domino_connection_success
color domino_game_board
color domino_piece_black
color domino_piece_white
color domino_player_highlight
color domino_primary
color domino_primary_dark
color domino_score_background
color domino_surface
color domino_text_primary
color domino_text_secondary
color domino_waiting
color highlighted_text_material_dark
color highlighted_text_material_light
color hint_foreground_material_dark
color hint_foreground_material_light
color link_text_material_dark
color link_text_material_light
color material_blue_grey_800
color material_blue_grey_900
color material_blue_grey_950
color material_deep_teal_200
color material_deep_teal_500
color primary_dark_material_dark
color primary_dark_material_light
color primary_material_dark
color primary_material_light
color primary_text_default_material_dark
color primary_text_default_material_light
color primary_text_disabled_material_dark
color primary_text_disabled_material_light
color ripple_material_dark
color ripple_material_light
color secondary_text_default_material_dark
color secondary_text_default_material_light
color secondary_text_disabled_material_dark
color secondary_text_disabled_material_light
color switch_thumb_disabled_material_dark
color switch_thumb_disabled_material_light
color switch_thumb_material_dark
color switch_thumb_material_light
color switch_thumb_normal_material_dark
color switch_thumb_normal_material_light
dimen abc_action_bar_content_inset_material
dimen abc_action_bar_default_height_material
dimen abc_action_bar_default_padding_material
dimen abc_action_bar_icon_vertical_padding_material
dimen abc_action_bar_navigation_padding_start_material
dimen abc_action_bar_overflow_padding_end_material
dimen abc_action_bar_overflow_padding_start_material
dimen abc_action_bar_progress_bar_size
dimen abc_action_bar_stacked_max_height
dimen abc_action_bar_stacked_tab_max_width
dimen abc_action_bar_subtitle_bottom_margin_material
dimen abc_action_bar_subtitle_top_margin_material
dimen abc_action_button_min_height_material
dimen abc_action_button_min_width_material
dimen abc_action_button_min_width_overflow_material
dimen abc_alert_dialog_button_bar_height
dimen abc_button_inset_horizontal_material
dimen abc_button_inset_vertical_material
dimen abc_button_padding_horizontal_material
dimen abc_button_padding_vertical_material
dimen abc_config_prefDialogWidth
dimen abc_control_corner_material
dimen abc_control_inset_material
dimen abc_control_padding_material
dimen abc_dialog_list_padding_vertical_material
dimen abc_dialog_min_width_major
dimen abc_dialog_min_width_minor
dimen abc_dialog_padding_material
dimen abc_dialog_padding_top_material
dimen abc_disabled_alpha_material_dark
dimen abc_disabled_alpha_material_light
dimen abc_dropdownitem_icon_width
dimen abc_dropdownitem_text_padding_left
dimen abc_dropdownitem_text_padding_right
dimen abc_edit_text_inset_bottom_material
dimen abc_edit_text_inset_horizontal_material
dimen abc_edit_text_inset_top_material
dimen abc_floating_window_z
dimen abc_list_item_padding_horizontal_material
dimen abc_panel_menu_list_width
dimen abc_search_view_preferred_width
dimen abc_search_view_text_min_width
dimen abc_switch_padding
dimen abc_text_size_body_1_material
dimen abc_text_size_body_2_material
dimen abc_text_size_button_material
dimen abc_text_size_caption_material
dimen abc_text_size_display_1_material
dimen abc_text_size_display_2_material
dimen abc_text_size_display_3_material
dimen abc_text_size_display_4_material
dimen abc_text_size_headline_material
dimen abc_text_size_large_material
dimen abc_text_size_medium_material
dimen abc_text_size_menu_material
dimen abc_text_size_small_material
dimen abc_text_size_subhead_material
dimen abc_text_size_subtitle_material_toolbar
dimen abc_text_size_title_material
dimen abc_text_size_title_material_toolbar
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen dialog_fixed_height_major
dimen dialog_fixed_height_minor
dimen dialog_fixed_width_major
dimen dialog_fixed_width_minor
dimen disabled_alpha_material_dark
dimen disabled_alpha_material_light
drawable abc_btn_borderless_material
drawable abc_btn_check_material
drawable abc_btn_default_mtrl_shape
drawable abc_btn_radio_material
drawable abc_cab_background_internal_bg
drawable abc_cab_background_top_material
drawable abc_dialog_material_background_dark
drawable abc_dialog_material_background_light
drawable abc_edit_text_material
drawable abc_item_background_holo_dark
drawable abc_item_background_holo_light
drawable abc_list_selector_background_transition_holo_dark
drawable abc_list_selector_background_transition_holo_light
drawable abc_list_selector_holo_dark
drawable abc_list_selector_holo_light
drawable abc_ratingbar_full_material
drawable abc_spinner_textfield_background_material
drawable abc_switch_thumb_material
drawable abc_tab_indicator_material
drawable abc_textfield_search_material
drawable domino_button_background
drawable domino_card_background
drawable domino_game_board_background
drawable domino_piece_background
drawable domino_player_highlight
id action_bar
id action_bar_activity_content
id action_bar_container
id action_bar_root
id action_bar_spinner
id action_bar_subtitle
id action_bar_title
id action_context_bar
id action_menu_divider
id action_menu_presenter
id action_mode_bar
id action_mode_bar_stub
id action_mode_close_button
id activity_chooser_view_content
id alertTitle
id buttonPanel
id cancel_button
id cancel_connection_button
id checkbox
id connection_status
id contentPanel
id custom
id customPanel
id decor_content_parent
id default_activity_button
id device_list
id device_name
id dialog
id difficulty_spinner
id domino_chain
id domino_set_spinner
id dot_bottom_1
id dot_bottom_2
id dot_bottom_3
id dot_top_1
id dot_top_2
id dot_top_3
id dropdown
id edit_query
id expand_activities_button
id expanded_menu
id game_board_scroll
id game_info
id game_settings
id game_status
id home
id icon
id image
id listMode
id list_item
id make_discoverable_button
id max_score_spinner
id menu_button
id multiply
id normal
id parentPanel
id pass_button
id player1_score
id player2_score
id player_area
id player_hand
id player_name_edit
id players_list
id progress_bar
id progress_circular
id progress_horizontal
id radio
id save_button
id scan_button
id score_area
id screen
id scrollView
id search_badge
id search_bar
id search_button
id search_close_btn
id search_edit_frame
id search_go_btn
id search_mag_icon
id search_plate
id search_src_text
id search_voice_btn
id select_dialog_listview
id shortcut
id speed_spinner
id split_action_bar
id src_atop
id src_in
id src_over
id start_button
id start_game_button
id status_text
id submit_area
id tabMode
id team_play_switch
id textSpacerNoButtons
id theme_spinner
id title
id title_template
id topPanel
id up
id wrap_content
integer abc_config_activityDefaultDur
integer abc_config_activityShortDur
integer abc_max_action_buttons
layout abc_action_bar_title_item
layout abc_action_bar_up_container
layout abc_action_bar_view_list_nav_layout
layout abc_action_menu_item_layout
layout abc_action_menu_layout
layout abc_action_mode_bar
layout abc_action_mode_close_item_material
layout abc_activity_chooser_view
layout abc_activity_chooser_view_list_item
layout abc_alert_dialog_material
layout abc_dialog_title_material
layout abc_expanded_menu_layout
layout abc_list_menu_item_checkbox
layout abc_list_menu_item_icon
layout abc_list_menu_item_layout
layout abc_list_menu_item_radio
layout abc_popup_menu_item_layout
layout abc_screen_content_include
layout abc_screen_simple
layout abc_screen_simple_overlay_action_mode
layout abc_screen_toolbar
layout abc_search_dropdown_item_icons_2line
layout abc_search_view
layout abc_select_dialog_material
layout abc_simple_dropdown_hint
layout activity_debug
layout domino_connection
layout domino_game_board
layout domino_game_setup
layout domino_main_menu
layout domino_piece_item
layout domino_settings
layout domino_waiting
layout list_item
layout select_dialog_item_material
layout select_dialog_multichoice_material
layout select_dialog_singlechoice_material
layout support_simple_spinner_dropdown_item
menu domino
menu menu_debug
mipmap ic_launcher
string abc_action_bar_home_description
string abc_action_bar_home_description_format
string abc_action_bar_home_subtitle_description_format
string abc_action_bar_up_description
string abc_action_menu_overflow_description
string abc_action_mode_done
string abc_activity_chooser_view_see_all
string abc_activitychooserview_choose_application
string abc_search_hint
string abc_searchview_description_clear
string abc_searchview_description_query
string abc_searchview_description_search
string abc_searchview_description_submit
string abc_searchview_description_voice
string abc_shareactionprovider_share_with
string abc_shareactionprovider_share_with_application
string abc_toolbar_collapse_description
string about_label
string action_settings
string ai1
string ai2
string ai3
string app_name
string blue
string button_new_game
string cancel
string clear_debug
string close
string connect
string connection_error
string continue_label
string create_group
string creator_team
string cuban_domino
string debug
string debug_info
string demo_mode
string devices
string different_server_version
string do_you_want_to_start
string domino_set
string empty_player_name
string error
string error_bluetooth_not_supported
string error_getting_game_data
string exit_game_msg
string exit_label
string fast
string game_creator
string game_draw
string game_info
string game_over
string game_setup
string getting_game_data
string hello
string hello_world
string i_passed
string its_better_if_you_start
string join_group
string joining_game
string let_me_start
string main_title
string max_score
string me
string must_select_device
string network
string new_game
string new_game_label
string next_round
string no
string no_devices
string no_let_me_start
string no_way_you_start
string normal
string ok
string ok_i_will_start
string ok_you_start
string pale
string pieces_28
string pieces_55
string player_passed
string player_playing
string player_thinking
string players
string ready
string ready_to_start
string retry
string round_over
string scanning
string score
string settings
string slow
string speed
string start
string start_game
string team_play
string theme
string title_activity_connection
string title_activity_debug
string title_activity_domino
string unable_to_join
string version
string waiting_for_new_round
string waiting_for_players
string winner_player
string yes
string your_name
string your_name_title
string your_partner_doesnt_wants_to_start
string your_partner_wants_to_start
style AlertDialog.AppCompat
style AlertDialog.AppCompat.Light
style Animation.AppCompat.Dialog
style Animation.AppCompat.DropDownUp
style AppTheme
style Base.AlertDialog.AppCompat
style Base.AlertDialog.AppCompat.Light
style Base.Animation.AppCompat.Dialog
style Base.Animation.AppCompat.DropDownUp
style Base.DialogWindowTitleBackground.AppCompat
style Base.DialogWindowTitle.AppCompat
style Base.TextAppearance.AppCompat
style Base.TextAppearance.AppCompat.Body1
style Base.TextAppearance.AppCompat.Body2
style Base.TextAppearance.AppCompat.Button
style Base.TextAppearance.AppCompat.Caption
style Base.TextAppearance.AppCompat.Display1
style Base.TextAppearance.AppCompat.Display2
style Base.TextAppearance.AppCompat.Display3
style Base.TextAppearance.AppCompat.Display4
style Base.TextAppearance.AppCompat.Headline
style Base.TextAppearance.AppCompat.Inverse
style Base.TextAppearance.AppCompat.Large
style Base.TextAppearance.AppCompat.Large.Inverse
style Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large
style Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small
style Base.TextAppearance.AppCompat.Medium
style Base.TextAppearance.AppCompat.Medium.Inverse
style Base.TextAppearance.AppCompat.Menu
style Base.TextAppearance.AppCompat.SearchResult
style Base.TextAppearance.AppCompat.SearchResult.Subtitle
style Base.TextAppearance.AppCompat.SearchResult.Title
style Base.TextAppearance.AppCompat.Small
style Base.TextAppearance.AppCompat.Small.Inverse
style Base.TextAppearance.AppCompat.Subhead
style Base.TextAppearance.AppCompat.Subhead.Inverse
style Base.TextAppearance.AppCompat.Title
style Base.TextAppearance.AppCompat.Title.Inverse
style Base.TextAppearance.AppCompat.Widget.ActionBar.Menu
style Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle
style Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse
style Base.TextAppearance.AppCompat.Widget.ActionBar.Title
style Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse
style Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle
style Base.TextAppearance.AppCompat.Widget.ActionMode.Title
style Base.TextAppearance.AppCompat.Widget.DropDownItem
style Base.TextAppearance.AppCompat.Widget.PopupMenu.Large
style Base.TextAppearance.AppCompat.Widget.PopupMenu.Small
style Base.TextAppearance.AppCompat.Widget.Switch
style Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem
style Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item
style Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle
style Base.TextAppearance.Widget.AppCompat.Toolbar.Title
style Base.ThemeOverlay.AppCompat
style Base.ThemeOverlay.AppCompat.ActionBar
style Base.ThemeOverlay.AppCompat.Dark
style Base.ThemeOverlay.AppCompat.Dark.ActionBar
style Base.ThemeOverlay.AppCompat.Light
style Base.Theme.AppCompat
style Base.Theme.AppCompat.CompactMenu
style Base.Theme.AppCompat.Dialog
style Base.Theme.AppCompat.DialogWhenLarge
style Base.Theme.AppCompat.Dialog.Alert
style Base.Theme.AppCompat.Dialog.FixedSize
style Base.Theme.AppCompat.Dialog.MinWidth
style Base.Theme.AppCompat.Light
style Base.Theme.AppCompat.Light.DarkActionBar
style Base.Theme.AppCompat.Light.Dialog
style Base.Theme.AppCompat.Light.DialogWhenLarge
style Base.Theme.AppCompat.Light.Dialog.Alert
style Base.Theme.AppCompat.Light.Dialog.FixedSize
style Base.Theme.AppCompat.Light.Dialog.MinWidth
style Base.V11.Theme.AppCompat.Dialog
style Base.V11.Theme.AppCompat.Light.Dialog
style Base.V21.Theme.AppCompat
style Base.V21.Theme.AppCompat.Dialog
style Base.V21.Theme.AppCompat.Light
style Base.V21.Theme.AppCompat.Light.Dialog
style Base.V7.Theme.AppCompat
style Base.V7.Theme.AppCompat.Dialog
style Base.V7.Theme.AppCompat.Light
style Base.V7.Theme.AppCompat.Light.Dialog
style Base.Widget.AppCompat.ActionBar
style Base.Widget.AppCompat.ActionBar.Solid
style Base.Widget.AppCompat.ActionBar.TabBar
style Base.Widget.AppCompat.ActionBar.TabText
style Base.Widget.AppCompat.ActionBar.TabView
style Base.Widget.AppCompat.ActionButton
style Base.Widget.AppCompat.ActionButton.CloseMode
style Base.Widget.AppCompat.ActionButton.Overflow
style Base.Widget.AppCompat.ActionMode
style Base.Widget.AppCompat.ActivityChooserView
style Base.Widget.AppCompat.AutoCompleteTextView
style Base.Widget.AppCompat.Button
style Base.Widget.AppCompat.ButtonBar
style Base.Widget.AppCompat.ButtonBar.AlertDialog
style Base.Widget.AppCompat.Button.Borderless
style Base.Widget.AppCompat.Button.Borderless.Colored
style Base.Widget.AppCompat.Button.ButtonBar.AlertDialog
style Base.Widget.AppCompat.Button.Small
style Base.Widget.AppCompat.CompoundButton.CheckBox
style Base.Widget.AppCompat.CompoundButton.RadioButton
style Base.Widget.AppCompat.CompoundButton.Switch
style Base.Widget.AppCompat.DrawerArrowToggle.Common
style Base.Widget.AppCompat.DropDownItem.Spinner
style Base.Widget.AppCompat.EditText
style Base.Widget.AppCompat.Light.ActionBar
style Base.Widget.AppCompat.Light.ActionBar.Solid
style Base.Widget.AppCompat.Light.ActionBar.TabBar
style Base.Widget.AppCompat.Light.ActionBar.TabText
style Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse
style Base.Widget.AppCompat.Light.ActionBar.TabView
style Base.Widget.AppCompat.Light.PopupMenu
style Base.Widget.AppCompat.Light.PopupMenu.Overflow
style Base.Widget.AppCompat.ListPopupWindow
style Base.Widget.AppCompat.ListView
style Base.Widget.AppCompat.ListView.DropDown
style Base.Widget.AppCompat.ListView.Menu
style Base.Widget.AppCompat.PopupMenu
style Base.Widget.AppCompat.PopupMenu.Overflow
style Base.Widget.AppCompat.PopupWindow
style Base.Widget.AppCompat.ProgressBar
style Base.Widget.AppCompat.ProgressBar.Horizontal
style Base.Widget.AppCompat.RatingBar
style Base.Widget.AppCompat.SearchView
style Base.Widget.AppCompat.SearchView.ActionBar
style Base.Widget.AppCompat.Spinner
style Base.Widget.AppCompat.Spinner.DropDown.ActionBar
style Base.Widget.AppCompat.Spinner.Underlined
style Base.Widget.AppCompat.TextView.SpinnerItem
style Base.Widget.AppCompat.Toolbar
style Base.Widget.AppCompat.Toolbar.Button.Navigation
style DominoButton
style DominoButton.Negative
style DominoButton.Neutral
style DominoButton.Positive
style DominoCard
style DominoText
style DominoText.Score
style DominoText.Title
style Platform.AppCompat
style Platform.AppCompat.Light
style Platform.ThemeOverlay.AppCompat.Dark
style Platform.ThemeOverlay.AppCompat.Light
style Platform.V11.AppCompat
style Platform.V11.AppCompat.Light
style Platform.V12.AppCompat
style Platform.V12.AppCompat.Light
style Platform.V14.AppCompat
style Platform.V14.AppCompat.Light
style RtlOverlay.Widget.AppCompat.ActionBar.TitleItem
style RtlOverlay.Widget.AppCompat.ActionButton.Overflow
style RtlOverlay.Widget.AppCompat.PopupMenuItem
style RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup
style RtlOverlay.Widget.AppCompat.PopupMenuItem.Text
style RtlOverlay.Widget.AppCompat.SearchView.MagIcon
style RtlOverlay.Widget.AppCompat.Search.DropDown
style RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1
style RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2
style RtlOverlay.Widget.AppCompat.Search.DropDown.Query
style RtlOverlay.Widget.AppCompat.Search.DropDown.Text
style RtlOverlay.Widget.AppCompat.Toolbar.Button.Navigation
style TextAppearance.AppCompat
style TextAppearance.AppCompat.Body1
style TextAppearance.AppCompat.Body2
style TextAppearance.AppCompat.Button
style TextAppearance.AppCompat.Caption
style TextAppearance.AppCompat.Display1
style TextAppearance.AppCompat.Display2
style TextAppearance.AppCompat.Display3
style TextAppearance.AppCompat.Display4
style TextAppearance.AppCompat.Headline
style TextAppearance.AppCompat.Inverse
style TextAppearance.AppCompat.Large
style TextAppearance.AppCompat.Large.Inverse
style TextAppearance.AppCompat.Light.SearchResult.Subtitle
style TextAppearance.AppCompat.Light.SearchResult.Title
style TextAppearance.AppCompat.Light.Widget.PopupMenu.Large
style TextAppearance.AppCompat.Light.Widget.PopupMenu.Small
style TextAppearance.AppCompat.Medium
style TextAppearance.AppCompat.Medium.Inverse
style TextAppearance.AppCompat.Menu
style TextAppearance.AppCompat.SearchResult.Subtitle
style TextAppearance.AppCompat.SearchResult.Title
style TextAppearance.AppCompat.Small
style TextAppearance.AppCompat.Small.Inverse
style TextAppearance.AppCompat.Subhead
style TextAppearance.AppCompat.Subhead.Inverse
style TextAppearance.AppCompat.Title
style TextAppearance.AppCompat.Title.Inverse
style TextAppearance.AppCompat.Widget.ActionBar.Menu
style TextAppearance.AppCompat.Widget.ActionBar.Subtitle
style TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse
style TextAppearance.AppCompat.Widget.ActionBar.Title
style TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse
style TextAppearance.AppCompat.Widget.ActionMode.Subtitle
style TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse
style TextAppearance.AppCompat.Widget.ActionMode.Title
style TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse
style TextAppearance.AppCompat.Widget.DropDownItem
style TextAppearance.AppCompat.Widget.PopupMenu.Large
style TextAppearance.AppCompat.Widget.PopupMenu.Small
style TextAppearance.AppCompat.Widget.Switch
style TextAppearance.AppCompat.Widget.TextView.SpinnerItem
style TextAppearance.Widget.AppCompat.ExpandedMenu.Item
style TextAppearance.Widget.AppCompat.Toolbar.Subtitle
style TextAppearance.Widget.AppCompat.Toolbar.Title
style ThemeOverlay.AppCompat
style ThemeOverlay.AppCompat.ActionBar
style ThemeOverlay.AppCompat.Dark
style ThemeOverlay.AppCompat.Dark.ActionBar
style ThemeOverlay.AppCompat.Light
style Theme.AppCompat
style Theme.AppCompat.CompactMenu
style Theme.AppCompat.Dialog
style Theme.AppCompat.DialogWhenLarge
style Theme.AppCompat.Dialog.Alert
style Theme.AppCompat.Dialog.MinWidth
style Theme.AppCompat.Light
style Theme.AppCompat.Light.DarkActionBar
style Theme.AppCompat.Light.Dialog
style Theme.AppCompat.Light.DialogWhenLarge
style Theme.AppCompat.Light.Dialog.Alert
style Theme.AppCompat.Light.Dialog.MinWidth
style Theme.AppCompat.Light.NoActionBar
style Theme.AppCompat.NoActionBar
style Widget.AppCompat.ActionBar
style Widget.AppCompat.ActionBar.Solid
style Widget.AppCompat.ActionBar.TabBar
style Widget.AppCompat.ActionBar.TabText
style Widget.AppCompat.ActionBar.TabView
style Widget.AppCompat.ActionButton
style Widget.AppCompat.ActionButton.CloseMode
style Widget.AppCompat.ActionButton.Overflow
style Widget.AppCompat.ActionMode
style Widget.AppCompat.ActivityChooserView
style Widget.AppCompat.AutoCompleteTextView
style Widget.AppCompat.Button
style Widget.AppCompat.ButtonBar
style Widget.AppCompat.ButtonBar.AlertDialog
style Widget.AppCompat.Button.Borderless
style Widget.AppCompat.Button.Borderless.Colored
style Widget.AppCompat.Button.ButtonBar.AlertDialog
style Widget.AppCompat.Button.Small
style Widget.AppCompat.CompoundButton.CheckBox
style Widget.AppCompat.CompoundButton.RadioButton
style Widget.AppCompat.CompoundButton.Switch
style Widget.AppCompat.DrawerArrowToggle
style Widget.AppCompat.DropDownItem.Spinner
style Widget.AppCompat.EditText
style Widget.AppCompat.Light.ActionBar
style Widget.AppCompat.Light.ActionBar.Solid
style Widget.AppCompat.Light.ActionBar.Solid.Inverse
style Widget.AppCompat.Light.ActionBar.TabBar
style Widget.AppCompat.Light.ActionBar.TabBar.Inverse
style Widget.AppCompat.Light.ActionBar.TabText
style Widget.AppCompat.Light.ActionBar.TabText.Inverse
style Widget.AppCompat.Light.ActionBar.TabView
style Widget.AppCompat.Light.ActionBar.TabView.Inverse
style Widget.AppCompat.Light.ActionButton
style Widget.AppCompat.Light.ActionButton.CloseMode
style Widget.AppCompat.Light.ActionButton.Overflow
style Widget.AppCompat.Light.ActionMode.Inverse
style Widget.AppCompat.Light.ActivityChooserView
style Widget.AppCompat.Light.AutoCompleteTextView
style Widget.AppCompat.Light.DropDownItem.Spinner
style Widget.AppCompat.Light.ListPopupWindow
style Widget.AppCompat.Light.ListView.DropDown
style Widget.AppCompat.Light.PopupMenu
style Widget.AppCompat.Light.PopupMenu.Overflow
style Widget.AppCompat.Light.SearchView
style Widget.AppCompat.Light.Spinner.DropDown.ActionBar
style Widget.AppCompat.ListPopupWindow
style Widget.AppCompat.ListView
style Widget.AppCompat.ListView.DropDown
style Widget.AppCompat.ListView.Menu
style Widget.AppCompat.PopupMenu
style Widget.AppCompat.PopupMenu.Overflow
style Widget.AppCompat.PopupWindow
style Widget.AppCompat.ProgressBar
style Widget.AppCompat.ProgressBar.Horizontal
style Widget.AppCompat.RatingBar
style Widget.AppCompat.SearchView
style Widget.AppCompat.SearchView.ActionBar
style Widget.AppCompat.Spinner
style Widget.AppCompat.Spinner.DropDown
style Widget.AppCompat.Spinner.DropDown.ActionBar
style Widget.AppCompat.Spinner.Underlined
style Widget.AppCompat.TextView.SpinnerItem
style Widget.AppCompat.Toolbar
style Widget.AppCompat.Toolbar.Button.Navigation
xml splits0
