{"logs": [{"outputFile": "ratmil.domino.app-mergeDebugResources-27:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\\transformed\\core-1.9.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "180", "startColumns": "4", "startOffsets": "11715", "endColumns": "100", "endOffsets": "11811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8679f45878a6127de1b97684ca2efa9a\\transformed\\material-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1238,1329,1398,1465,1565,1628,1693,1754,1822,1884,1942,2056,2116,2177,2234,2307,2430,2511,2591,2739,2820,2901,2990,3043,3097,3163,3241,3321,3405,3477,3551,3624,3694,3785,3856,3946,4041,4115,4198,4291,4340,4409,4495,4580,4642,4706,4769,4878,4970,5067,5160,5217,5275", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,88,52,53,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,56,57,79", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1233,1324,1393,1460,1560,1623,1688,1749,1817,1879,1937,2051,2111,2172,2229,2302,2425,2506,2586,2734,2815,2896,2985,3038,3092,3158,3236,3316,3400,3472,3546,3619,3689,3780,3851,3941,4036,4110,4193,4286,4335,4404,4490,4575,4637,4701,4764,4873,4965,5062,5155,5212,5270,5350"}, "to": {"startLines": "2,46,47,48,49,50,53,54,56,75,78,89,90,96,97,98,99,100,101,102,103,104,105,106,107,108,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,3038,3116,3192,3276,3368,3547,3648,3811,4916,5104,5708,5777,6124,6224,6287,6352,6413,6481,6543,6601,6715,6775,6836,6893,6966,7173,7254,7334,7482,7563,7644,7733,7786,7840,7906,7984,8064,8148,8220,8294,8367,8437,8528,8599,8689,8784,8858,8941,9034,9083,9152,9238,9323,9385,9449,9512,9621,9713,9810,9903,9960,10718", "endLines": "9,46,47,48,49,50,53,54,56,75,78,89,90,96,97,98,99,100,101,102,103,104,105,106,107,108,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,161", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,88,52,53,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,56,57,79", "endOffsets": "460,3111,3187,3271,3363,3446,3643,3762,3883,4974,5190,5772,5839,6219,6282,6347,6408,6476,6538,6596,6710,6770,6831,6888,6961,7084,7249,7329,7477,7558,7639,7728,7781,7835,7901,7979,8059,8143,8215,8289,8362,8432,8523,8594,8684,8779,8853,8936,9029,9078,9147,9233,9318,9380,9444,9507,9616,9708,9805,9898,9955,10013,10793"}}, {"source": "D:\\Desktop\\0000\\5\\Domino_v0.4(3)_base_src\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "118,192,270,363,433,511,565,635,720,768,814,866,939,1013,1082,1157,1231,1305,1394,1505,1549,1603,1639,1677,1714,1758,1796,1851,1892,1936,1976,2018,2078,2128,2180,2238,2304,2353,2408,2465,2509,2579,2645,2700,2760,2798,2880,2964,3045,3089,3127,3180,3233,3279,3330,3382,3441,3505,3560,3602,3678,3730,3785,3836,3882,3931,3966,4033,4076,4124,4178,4232,4266,4319,4377,4435,4475,4512,4571,4628,4666,4712,4758,4814,4872,4931,4976,5038,5092,5141,5193,5244,5286,5333,5371,5412,5451,5501,5549,5590,5658,5720,5777,5848,5892,5963,6030,6083,6119,6163,6212,6299", "endColumns": "73,77,92,69,77,53,69,84,47,45,51,72,73,68,74,73,73,88,69,43,53,35,37,36,43,37,54,40,43,39,41,59,49,51,57,65,48,54,56,43,69,65,54,59,37,81,83,80,43,37,52,52,45,50,51,58,63,54,41,75,51,54,50,45,48,34,66,42,47,53,53,33,52,57,57,39,36,58,56,37,45,45,55,57,58,44,61,53,48,51,50,41,46,37,40,38,49,47,40,67,61,56,70,43,70,66,52,35,43,48,86,74", "endOffsets": "187,265,358,428,506,560,630,715,763,809,861,934,1008,1077,1152,1226,1300,1389,1459,1544,1598,1634,1672,1709,1753,1791,1846,1887,1931,1971,2013,2073,2123,2175,2233,2299,2348,2403,2460,2504,2574,2640,2695,2755,2793,2875,2959,3040,3084,3122,3175,3228,3274,3325,3377,3436,3500,3555,3597,3673,3725,3780,3831,3877,3926,3961,4028,4071,4119,4173,4227,4261,4314,4372,4430,4470,4507,4566,4623,4661,4707,4753,4809,4867,4926,4971,5033,5087,5136,5188,5239,5281,5328,5366,5407,5446,5496,5544,5585,5653,5715,5772,5843,5887,5958,6025,6078,6114,6158,6207,6294,6369"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,51,52,55,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,76,77,79,80,81,82,83,84,85,86,87,88,91,92,93,94,95,109,110,147,148,149,150,151,152,153,154,155,156,157,158,159,160,162,163,164,165,166,167,168,169,170,171,172,173,175,176,177,178,179,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "465,539,617,710,780,858,912,982,1067,1115,2097,2149,2222,2296,2365,2440,2514,2588,2677,2747,2791,2845,2881,2919,2956,3000,3451,3506,3767,3888,3928,3970,4030,4080,4132,4190,4256,4305,4360,4417,4461,4531,4597,4652,4712,4750,4832,4979,5060,5195,5233,5286,5339,5385,5436,5488,5547,5611,5666,5844,5920,5972,6027,6078,7089,7138,10018,10085,10128,10176,10230,10284,10318,10371,10429,10487,10527,10564,10623,10680,10798,10844,10890,10946,11004,11063,11108,11170,11224,11273,11325,11376,11500,11547,11585,11626,11665,11816,11864,11905,11973,12035,12092,12163,12207,12278,12345,12398,12434,12478,12527,12614", "endColumns": "73,77,92,69,77,53,69,84,47,45,51,72,73,68,74,73,73,88,69,43,53,35,37,36,43,37,54,40,43,39,41,59,49,51,57,65,48,54,56,43,69,65,54,59,37,81,83,80,43,37,52,52,45,50,51,58,63,54,41,75,51,54,50,45,48,34,66,42,47,53,53,33,52,57,57,39,36,58,56,37,45,45,55,57,58,44,61,53,48,51,50,41,46,37,40,38,49,47,40,67,61,56,70,43,70,66,52,35,43,48,86,74", "endOffsets": "534,612,705,775,853,907,977,1062,1110,1156,2144,2217,2291,2360,2435,2509,2583,2672,2742,2786,2840,2876,2914,2951,2995,3033,3501,3542,3806,3923,3965,4025,4075,4127,4185,4251,4300,4355,4412,4456,4526,4592,4647,4707,4745,4827,4911,5055,5099,5228,5281,5334,5380,5431,5483,5542,5606,5661,5703,5915,5967,6022,6073,6119,7133,7168,10080,10123,10171,10225,10279,10313,10366,10424,10482,10522,10559,10618,10675,10713,10839,10885,10941,10999,11058,11103,11165,11219,11268,11320,11371,11413,11542,11580,11621,11660,11710,11859,11900,11968,12030,12087,12158,12202,12273,12340,12393,12429,12473,12522,12609,12684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9ae72c11e6e08c08a7e177bedd18d43\\transformed\\appcompat-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "10,11,12,13,14,15,16,17,18,19,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "880,971,1064,1156,1250,1350,1443,1538,1631,1722,2759", "endColumns": "90,92,91,93,99,92,94,92,90,93,81", "endOffsets": "966,1059,1151,1245,1345,1438,1533,1626,1717,1811,2836"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1161,1252,1345,1437,1531,1631,1724,1819,1912,2003,11418", "endColumns": "90,92,91,93,99,92,94,92,90,93,81", "endOffsets": "1247,1340,1432,1526,1626,1719,1814,1907,1998,2092,11495"}}]}]}