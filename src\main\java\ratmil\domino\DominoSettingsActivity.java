package ratmil.domino;

import android.app.Activity;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.Switch;

/**
 * Settings activity for the Domino game
 */
public class DominoSettingsActivity extends Activity {
    
    private static final String PREFS_NAME = "DominoSettings";
    private static final String PREF_MAX_SCORE = "max_score";
    private static final String PREF_DOMINO_SET = "domino_set";
    private static final String PREF_TEAM_PLAY = "team_play";
    private static final String PREF_SPEED = "speed";
    private static final String PREF_THEME = "theme";
    
    private Spinner maxScoreSpinner;
    private Spinner dominoSetSpinner;
    private Switch teamPlaySwitch;
    private Spinner speedSpinner;
    private Spinner themeSpinner;
    private Button saveButton;
    private Button cancelButton;
    
    private SharedPreferences settings;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.domino_settings);
        
        settings = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        
        initializeViews();
        setupSpinners();
        loadSettings();
        setupClickListeners();
    }
    
    private void initializeViews() {
        maxScoreSpinner = findViewById(R.id.max_score_spinner);
        dominoSetSpinner = findViewById(R.id.domino_set_spinner);
        teamPlaySwitch = findViewById(R.id.team_play_switch);
        speedSpinner = findViewById(R.id.speed_spinner);
        themeSpinner = findViewById(R.id.theme_spinner);
        saveButton = findViewById(R.id.save_button);
        cancelButton = findViewById(R.id.cancel_button);
    }
    
    private void setupSpinners() {
        // Max Score options
        String[] maxScoreOptions = {"50", "100", "150", "200", "300", "500"};
        ArrayAdapter<String> maxScoreAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, maxScoreOptions);
        maxScoreAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        maxScoreSpinner.setAdapter(maxScoreAdapter);
        
        // Domino Set options
        String[] dominoSetOptions = {getString(R.string.pieces_28), getString(R.string.pieces_55)};
        ArrayAdapter<String> dominoSetAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, dominoSetOptions);
        dominoSetAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        dominoSetSpinner.setAdapter(dominoSetAdapter);
        
        // Speed options
        String[] speedOptions = {getString(R.string.slow), getString(R.string.normal), getString(R.string.fast)};
        ArrayAdapter<String> speedAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, speedOptions);
        speedAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        speedSpinner.setAdapter(speedAdapter);
        
        // Theme options
        String[] themeOptions = {"Green", "Blue", "Red", "Yellow", "Dark"};
        ArrayAdapter<String> themeAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, themeOptions);
        themeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        themeSpinner.setAdapter(themeAdapter);
    }
    
    private void loadSettings() {
        // Load saved settings
        int maxScore = settings.getInt(PREF_MAX_SCORE, 100);
        setSpinnerValue(maxScoreSpinner, String.valueOf(maxScore));
        
        int dominoSet = settings.getInt(PREF_DOMINO_SET, 0);
        dominoSetSpinner.setSelection(dominoSet);
        
        boolean teamPlay = settings.getBoolean(PREF_TEAM_PLAY, false);
        teamPlaySwitch.setChecked(teamPlay);
        
        int speed = settings.getInt(PREF_SPEED, 1); // Default to Normal
        speedSpinner.setSelection(speed);
        
        int theme = settings.getInt(PREF_THEME, 0); // Default to Green
        themeSpinner.setSelection(theme);
    }
    
    private void setSpinnerValue(Spinner spinner, String value) {
        ArrayAdapter<String> adapter = (ArrayAdapter<String>) spinner.getAdapter();
        for (int i = 0; i < adapter.getCount(); i++) {
            if (adapter.getItem(i).equals(value)) {
                spinner.setSelection(i);
                break;
            }
        }
    }
    
    private void setupClickListeners() {
        saveButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveSettings();
            }
        });
        
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }
    
    private void saveSettings() {
        SharedPreferences.Editor editor = settings.edit();
        
        // Save max score
        String maxScoreStr = (String) maxScoreSpinner.getSelectedItem();
        editor.putInt(PREF_MAX_SCORE, Integer.parseInt(maxScoreStr));
        
        // Save domino set
        editor.putInt(PREF_DOMINO_SET, dominoSetSpinner.getSelectedItemPosition());
        
        // Save team play
        editor.putBoolean(PREF_TEAM_PLAY, teamPlaySwitch.isChecked());
        
        // Save speed
        editor.putInt(PREF_SPEED, speedSpinner.getSelectedItemPosition());
        
        // Save theme
        editor.putInt(PREF_THEME, themeSpinner.getSelectedItemPosition());
        
        editor.apply();
        
        // Apply theme change if needed
        applyTheme();
        
        finish();
    }
    
    private void applyTheme() {
        // Apply the selected theme
        int themeIndex = themeSpinner.getSelectedItemPosition();
        
        // This would typically restart the activity or apply theme changes
        // For now, we'll just save the preference
        // In a full implementation, you would apply the theme colors here
    }
    
    /**
     * Get saved settings
     */
    public static class GameSettings {
        public static int getMaxScore(Activity activity) {
            SharedPreferences prefs = activity.getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            return prefs.getInt(PREF_MAX_SCORE, 100);
        }
        
        public static boolean isDoubleNineSet(Activity activity) {
            SharedPreferences prefs = activity.getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            return prefs.getInt(PREF_DOMINO_SET, 0) == 1;
        }
        
        public static boolean isTeamPlay(Activity activity) {
            SharedPreferences prefs = activity.getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            return prefs.getBoolean(PREF_TEAM_PLAY, false);
        }
        
        public static int getSpeed(Activity activity) {
            SharedPreferences prefs = activity.getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            return prefs.getInt(PREF_SPEED, 1);
        }
        
        public static int getTheme(Activity activity) {
            SharedPreferences prefs = activity.getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
            return prefs.getInt(PREF_THEME, 0);
        }
    }
}
