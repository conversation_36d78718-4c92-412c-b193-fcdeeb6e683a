package ratmil.domino;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import java.util.List;

import ratmil.domino.ai.DominoAI;
import ratmil.domino.model.DominoGame;
import ratmil.domino.model.DominoPiece;
import ratmil.domino.model.GameBoard;
import ratmil.domino.model.Player;
// import ratmil.domino.ui.DominoPieceView;
// import ratmil.domino.ui.GameBoardView;

/**
 * Main game activity where the domino game is played
 */
public class DominoActivity extends Activity {
    
    private DominoGame game;
    private DominoAI ai;
    private Handler aiHandler;
    private boolean isDemoMode;
    
    // UI Components
    private TextView player1ScoreText;
    private TextView player2ScoreText;
    private TextView gameStatusText;
    private LinearLayout playerHandLayout;
    private GameBoardView gameBoardView;
    private Button passButton;
    private Button menuButton;
    private Button startButton;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.domino_game_board);
        
        initializeGame();
        initializeViews();
        setupClickListeners();
        
        if (game != null) {
            updateUI();
        }
    }
    
    private void initializeGame() {
        isDemoMode = getIntent().getBooleanExtra("demo_mode", false);
        
        if (isDemoMode) {
            game = (DominoGame) getIntent().getSerializableExtra("game_object");
        } else {
            // Create new game based on intent extras
            DominoGame.GameMode gameMode = DominoGame.GameMode.valueOf(
                getIntent().getStringExtra("game_mode"));
            game = new DominoGame(gameMode);
            
            // Add human player
            game.addPlayer(new Player(getString(R.string.me), Player.PlayerType.HUMAN));
            
            // Add AI players
            game.addPlayer(new Player(getString(R.string.ai1), Player.PlayerType.AI_HARD));
            game.addPlayer(new Player(getString(R.string.ai2), Player.PlayerType.AI_MEDIUM));
            game.addPlayer(new Player(getString(R.string.ai3), Player.PlayerType.AI_EASY));
        }
        
        ai = new DominoAI();
        aiHandler = new Handler();
    }
    
    private void initializeViews() {
        player1ScoreText = findViewById(R.id.player1_score);
        player2ScoreText = findViewById(R.id.player2_score);
        gameStatusText = findViewById(R.id.game_status);
        playerHandLayout = findViewById(R.id.player_hand);
        gameBoardView = findViewById(R.id.game_board_view);
        passButton = findViewById(R.id.pass_button);
        menuButton = findViewById(R.id.menu_button);
        startButton = findViewById(R.id.start_button);
    }
    
    private void setupClickListeners() {
        passButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                passTurn();
            }
        });
        
        menuButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showGameMenu();
            }
        });
        
        startButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startGame();
            }
        });
    }
    
    private void startGame() {
        if (game.getGameState() == DominoGame.GameState.WAITING_FOR_PLAYERS) {
            game.startGame();
            updateUI();
            
            // Start AI turn if needed
            if (game.getCurrentPlayer().getType() != Player.PlayerType.HUMAN) {
                scheduleAIMove();
            }
        }
    }
    
    private void passTurn() {
        Player currentPlayer = game.getCurrentPlayer();
        if (currentPlayer.getType() == Player.PlayerType.HUMAN) {
            game.passTurn(currentPlayer);
            updateUI();
            
            if (game.getGameState() == DominoGame.GameState.PLAYING) {
                scheduleAIMove();
            }
        }
    }
    
    private void playPiece(DominoPiece piece, GameBoard.PlacementSide side) {
        Player currentPlayer = game.getCurrentPlayer();
        
        if (game.playPiece(currentPlayer, piece, side)) {
            updateUI();
            
            if (game.getGameState() == DominoGame.GameState.ROUND_OVER) {
                showRoundOverDialog();
            } else if (game.getGameState() == DominoGame.GameState.GAME_OVER) {
                showGameOverDialog();
            } else if (game.getGameState() == DominoGame.GameState.PLAYING) {
                scheduleAIMove();
            }
        } else {
            Toast.makeText(this, R.string.error, Toast.LENGTH_SHORT).show();
        }
    }
    
    private void scheduleAIMove() {
        Player currentPlayer = game.getCurrentPlayer();
        
        if (currentPlayer.getType() != Player.PlayerType.HUMAN) {
            // Delay AI move for better user experience
            aiHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    makeAIMove();
                }
            }, isDemoMode ? 1000 : 2000); // Faster in demo mode
        }
    }
    
    private void makeAIMove() {
        Player currentPlayer = game.getCurrentPlayer();
        
        if (currentPlayer.getType() != Player.PlayerType.HUMAN) {
            DominoAI.AIMove aiMove = ai.makeMove(currentPlayer, game.getBoard(), game.getPlayers());
            
            if (aiMove.getMoveType() == DominoAI.AIMove.MoveType.PLAY) {
                playPiece(aiMove.getPiece(), aiMove.getSide());
            } else {
                game.passTurn(currentPlayer);
                updateUI();
                
                if (game.getGameState() == DominoGame.GameState.PLAYING) {
                    scheduleAIMove();
                }
            }
        }
    }
    
    private void updateUI() {
        updateScores();
        updateGameStatus();
        updatePlayerHand();
        updateGameBoard();
        updateButtons();
    }
    
    private void updateScores() {
        List<Player> players = game.getPlayers();
        if (players.size() >= 2) {
            player1ScoreText.setText(players.get(0).getName() + ": " + players.get(0).getScore());
            player2ScoreText.setText(players.get(1).getName() + ": " + players.get(1).getScore());
        }
    }
    
    private void updateGameStatus() {
        switch (game.getGameState()) {
            case WAITING_FOR_PLAYERS:
                gameStatusText.setText(R.string.ready);
                break;
            case PLAYING:
                Player currentPlayer = game.getCurrentPlayer();
                if (currentPlayer.getType() == Player.PlayerType.HUMAN) {
                    gameStatusText.setText(R.string.ready_to_start);
                } else {
                    gameStatusText.setText(getString(R.string.player_thinking, currentPlayer.getName()));
                }
                break;
            case ROUND_OVER:
                gameStatusText.setText(R.string.round_over);
                break;
            case GAME_OVER:
                gameStatusText.setText(R.string.game_over);
                break;
        }
    }
    
    private void updatePlayerHand() {
        playerHandLayout.removeAllViews();
        
        Player humanPlayer = findHumanPlayer();
        if (humanPlayer != null) {
            for (DominoPiece piece : humanPlayer.getHand()) {
                DominoPieceView pieceView = new DominoPieceView(this, piece);
                pieceView.setOnClickListener(new PieceClickListener(piece));
                playerHandLayout.addView(pieceView);
            }
        }
    }
    
    private void updateGameBoard() {
        if (gameBoardView != null) {
            gameBoardView.updateBoard(game.getBoard());
        }
    }
    
    private void updateButtons() {
        Player currentPlayer = game.getCurrentPlayer();
        boolean isHumanTurn = currentPlayer != null && currentPlayer.getType() == Player.PlayerType.HUMAN;
        boolean isPlaying = game.getGameState() == DominoGame.GameState.PLAYING;
        
        passButton.setEnabled(isHumanTurn && isPlaying);
        startButton.setVisibility(game.getGameState() == DominoGame.GameState.WAITING_FOR_PLAYERS ? 
            View.VISIBLE : View.GONE);
    }
    
    private Player findHumanPlayer() {
        for (Player player : game.getPlayers()) {
            if (player.getType() == Player.PlayerType.HUMAN) {
                return player;
            }
        }
        return null;
    }
    
    private void showRoundOverDialog() {
        // Implementation for round over dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(R.string.round_over);
        builder.setMessage("Round " + game.getRoundNumber() + " completed!");
        builder.setPositiveButton(R.string.continue_label, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                updateUI();
                scheduleAIMove();
            }
        });
        builder.show();
    }
    
    private void showGameOverDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(R.string.game_over);
        builder.setMessage(getString(R.string.winner_player, game.getWinner().getName()));
        builder.setPositiveButton(R.string.new_game, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                startGame();
            }
        });
        builder.setNegativeButton(R.string.exit_label, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                finish();
            }
        });
        builder.show();
    }
    
    private void showGameMenu() {
        // Implementation for game menu
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.domino, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        
        if (id == R.id.game_info) {
            // Show game info
            return true;
        } else if (id == R.id.game_settings) {
            // Show settings
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    private class PieceClickListener implements View.OnClickListener {
        private DominoPiece piece;
        
        public PieceClickListener(DominoPiece piece) {
            this.piece = piece;
        }
        
        @Override
        public void onClick(View v) {
            if (game.getCurrentPlayer().getType() == Player.PlayerType.HUMAN) {
                showPlacementDialog(piece);
            }
        }
    }
    
    private void showPlacementDialog(DominoPiece piece) {
        List<GameBoard.PlacementOption> options = game.getBoard().getPlacementOptions(piece);
        
        if (options.isEmpty()) {
            Toast.makeText(this, "Cannot play this piece", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (options.size() == 1) {
            playPiece(piece, options.get(0).getSide());
        } else {
            // Show dialog to choose placement side
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle("Choose placement");
            
            String[] choices = new String[options.size()];
            for (int i = 0; i < options.size(); i++) {
                choices[i] = options.get(i).getSide().name();
            }
            
            builder.setItems(choices, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    playPiece(piece, options.get(which).getSide());
                }
            });
            
            builder.show();
        }
    }
}
