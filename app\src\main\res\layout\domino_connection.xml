<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/domino_background"
    android:padding="16dp">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/title_activity_connection"
        style="@style/DominoText.Title"
        android:background="@drawable/domino_card_background"
        android:layout_marginBottom="16dp" />

    <!-- Status Text -->
    <TextView
        android:id="@+id/status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/scanning"
        style="@style/DominoText"
        android:gravity="center"
        android:background="@drawable/domino_card_background"
        android:padding="16dp"
        android:layout_marginBottom="16dp" />

    <!-- Device List -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/devices"
        style="@style/DominoText"
        android:layout_marginBottom="8dp" />

    <ListView
        android:id="@+id/device_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/domino_card_background"
        android:padding="8dp"
        android:divider="@color/domino_text_secondary"
        android:dividerHeight="1dp"
        android:layout_marginBottom="16dp" />

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/scan_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/scanning"
            style="@style/DominoButton.Neutral"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/make_discoverable_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Make Discoverable"
            style="@style/DominoButton.Positive"
            android:layout_marginHorizontal="4dp"
            android:visibility="gone" />

        <Button
            android:id="@+id/cancel_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/cancel"
            style="@style/DominoButton.Negative"
            android:layout_marginStart="8dp" />
    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/must_select_device"
        style="@style/DominoText"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:textSize="12sp" />
</LinearLayout>
