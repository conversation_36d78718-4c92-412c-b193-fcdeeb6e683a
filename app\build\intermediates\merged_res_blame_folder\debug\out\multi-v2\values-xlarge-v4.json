{"logs": [{"outputFile": "ratmil.domino.app-mergeDebugResources-27:/values-xlarge-v4/values-xlarge-v4.xml", "map": [{"source": "D:\\Desktop\\0000\\5\\Domino_v0.4(3)_base_src\\app\\src\\main\\res\\values-xlarge\\dimens.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,123", "endColumns": "67,71", "endOffsets": "118,190"}, "to": {"startLines": "9,10", "startColumns": "4,4", "startOffsets": "550,618", "endColumns": "67,71", "endOffsets": "613,685"}}, {"source": "D:\\Desktop\\0000\\5\\Domino_v0.4(3)_base_src\\app\\src\\main\\res\\values-xlarge\\integers.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "54", "endOffsets": "105"}, "to": {"startLines": "11", "startColumns": "4", "startOffsets": "690", "endColumns": "54", "endOffsets": "740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9ae72c11e6e08c08a7e177bedd18d43\\transformed\\appcompat-1.6.1\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}, "to": {"startLines": "3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4", "startOffsets": "132,203,274,344,414,482", "endColumns": "70,70,69,69,67,67", "endOffsets": "198,269,339,409,477,545"}}, {"source": "D:\\Desktop\\0000\\5\\Domino_v0.4(3)_base_src\\app\\src\\main\\res\\values-xlarge\\bools.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "76", "endOffsets": "127"}}]}]}