<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:orientation="vertical"
    android:id="@id/parentPanel"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:orientation="vertical"
        android:id="@id/topPanel"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:gravity="start|center"
            android:orientation="horizontal"
            android:id="@id/title_template"
            android:paddingLeft="?dialogPreferredPadding"
            android:paddingTop="@dimen/abc_dialog_padding_top_material"
            android:paddingRight="?dialogPreferredPadding"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@android:id/icon"
                android:layout_width="32.0dip"
                android:layout_height="32.0dip"
                android:src="@null"
                android:scaleType="fitCenter"
                android:layout_marginEnd="8.0dip" />
            <android.support.v7.internal.widget.DialogTitle
                android:ellipsize="end"
                android:id="@id/alertTitle"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textAlignment="viewStart"
                style="?android:windowTitleStyle" />
        </LinearLayout>
    </LinearLayout>
    <FrameLayout
        android:id="@id/contentPanel"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:minHeight="48.0dip"
        android:layout_weight="1.0">
        <ScrollView
            android:id="@id/scrollView"
            android:clipToPadding="false"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@android:id/message"
                    android:paddingLeft="?dialogPreferredPadding"
                    android:paddingTop="@dimen/abc_dialog_padding_top_material"
                    android:paddingRight="?dialogPreferredPadding"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    style="@style/TextAppearance.AppCompat.Subhead" />
                <View
                    android:id="@id/textSpacerNoButtons"
                    android:visibility="gone"
                    android:layout_width="0.0dip"
                    android:layout_height="@dimen/abc_dialog_padding_top_material" />
            </LinearLayout>
        </ScrollView>
    </FrameLayout>
    <FrameLayout
        android:id="@id/customPanel"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:minHeight="48.0dip"
        android:layout_weight="1.0">
        <FrameLayout
            android:id="@id/custom"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content" />
    </FrameLayout>
    <LinearLayout
        android:gravity="bottom"
        android:orientation="horizontal"
        android:id="@id/buttonPanel"
        android:paddingLeft="12.0dip"
        android:paddingTop="8.0dip"
        android:paddingRight="12.0dip"
        android:paddingBottom="8.0dip"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layoutDirection="locale"
        style="?buttonBarStyle">
        <Button
            android:id="@android:id/button3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="?buttonBarNeutralButtonStyle" />
        <android.support.v4.widget.Space
            android:visibility="invisible"
            android:layout_width="0.0dip"
            android:layout_height="0.0dip"
            android:layout_weight="1.0" />
        <Button
            android:id="@android:id/button2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="?buttonBarNegativeButtonStyle" />
        <Button
            android:id="@android:id/button1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="?buttonBarPositiveButtonStyle" />
    </LinearLayout>
</LinearLayout>