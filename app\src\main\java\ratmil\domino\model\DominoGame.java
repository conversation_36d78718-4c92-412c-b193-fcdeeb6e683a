package ratmil.domino.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

/**
 * Main game logic for Cuban-style domino game
 */
public class DominoGame implements Serializable {
    private static final long serialVersionUID = 1L;
    
    public enum GameState {
        WAITING_FOR_PLAYERS,
        STARTING,
        PLAYING,
        ROUND_OVER,
        GAME_OVER
    }
    
    public enum GameMode {
        SINGLE_PLAYER,
        MULTIPLAYER_LOCAL,
        MULTIPLAYER_BLUETOOTH
    }
    
    private List<Player> players;
    private GameBoard board;
    private List<DominoPiece> boneyard;
    private int currentPlayerIndex;
    private GameState gameState;
    private GameMode gameMode;
    private int maxScore;
    private boolean isTeamPlay;
    private Random random;
    private Player winner;
    private int roundNumber;
    
    // Game settings
    private boolean useDoubleNineSet;
    private int piecesPerPlayer;
    
    public DominoGame(GameMode gameMode) {
        this.gameMode = gameMode;
        this.players = new ArrayList<>();
        this.board = new GameBoard();
        this.boneyard = new ArrayList<>();
        this.currentPlayerIndex = 0;
        this.gameState = GameState.WAITING_FOR_PLAYERS;
        this.maxScore = 100; // Default Cuban domino score
        this.isTeamPlay = false;
        this.random = new Random();
        this.roundNumber = 1;
        this.useDoubleNineSet = false;
        this.piecesPerPlayer = 7; // Standard for 4 players
    }
    
    /**
     * Add a player to the game
     */
    public boolean addPlayer(Player player) {
        if (players.size() >= 4) {
            return false; // Maximum 4 players
        }
        players.add(player);
        return true;
    }
    
    /**
     * Start a new game
     */
    public void startGame() {
        if (players.size() < 2) {
            throw new IllegalStateException("Need at least 2 players to start");
        }
        
        gameState = GameState.STARTING;
        roundNumber = 1;
        
        // Reset all players
        for (Player player : players) {
            player.resetForNewGame();
        }
        
        startNewRound();
    }
    
    /**
     * Start a new round
     */
    public void startNewRound() {
        gameState = GameState.STARTING;
        board.reset();
        
        // Reset players for new round
        for (Player player : players) {
            player.resetForNewRound();
        }
        
        // Create and shuffle pieces
        createBoneyard();
        dealPieces();
        
        // Determine starting player
        determineStartingPlayer();
        
        gameState = GameState.PLAYING;
    }
    
    /**
     * Create the boneyard (all domino pieces)
     */
    private void createBoneyard() {
        boneyard.clear();
        
        DominoPiece[] pieces = useDoubleNineSet ? 
            DominoPiece.createDoubleNineSet() : 
            DominoPiece.createStandardSet();
            
        for (DominoPiece piece : pieces) {
            boneyard.add(piece);
        }
        
        Collections.shuffle(boneyard, random);
    }
    
    /**
     * Deal pieces to all players
     */
    private void dealPieces() {
        // Adjust pieces per player based on number of players
        if (players.size() == 2) {
            piecesPerPlayer = useDoubleNineSet ? 10 : 7;
        } else if (players.size() == 3) {
            piecesPerPlayer = useDoubleNineSet ? 8 : 6;
        } else {
            piecesPerPlayer = useDoubleNineSet ? 7 : 7;
        }
        
        for (int i = 0; i < piecesPerPlayer; i++) {
            for (Player player : players) {
                if (!boneyard.isEmpty()) {
                    player.addPiece(boneyard.remove(0));
                }
            }
        }
    }
    
    /**
     * Determine which player starts the round
     */
    private void determineStartingPlayer() {
        // In Cuban domino, player with highest double starts
        Player startingPlayer = null;
        DominoPiece highestDouble = null;
        
        for (Player player : players) {
            DominoPiece playerHighest = player.getHighestDouble();
            if (playerHighest != null) {
                if (highestDouble == null || 
                    playerHighest.getTopValue() > highestDouble.getTopValue()) {
                    highestDouble = playerHighest;
                    startingPlayer = player;
                }
            }
        }
        
        if (startingPlayer != null) {
            currentPlayerIndex = players.indexOf(startingPlayer);
            // Auto-play the highest double
            playPiece(startingPlayer, highestDouble, GameBoard.PlacementSide.FIRST);
        } else {
            // No doubles, player with highest total starts
            Player highestTotal = players.get(0);
            for (Player player : players) {
                if (player.getHandValue() > highestTotal.getHandValue()) {
                    highestTotal = player;
                }
            }
            currentPlayerIndex = players.indexOf(highestTotal);
        }
        
        getCurrentPlayer().setMyTurn(true);
    }
    
    /**
     * Play a piece on the board
     */
    public boolean playPiece(Player player, DominoPiece piece, GameBoard.PlacementSide side) {
        if (!isValidMove(player, piece)) {
            return false;
        }
        
        boolean success = false;
        switch (side) {
            case FIRST:
                success = board.placeFirstPiece(piece);
                break;
            case LEFT:
                success = board.placeOnLeft(piece);
                break;
            case RIGHT:
                success = board.placeOnRight(piece);
                break;
        }
        
        if (success) {
            player.removePiece(piece);
            player.setHasPassed(false);
            
            // Check for round end
            if (player.hasNoPieces()) {
                endRound(player);
                return true;
            }
            
            // Check for blocked game
            if (board.isBlocked(players)) {
                endRoundBlocked();
                return true;
            }
            
            nextPlayer();
        }
        
        return success;
    }
    
    /**
     * Player passes their turn
     */
    public void passTurn(Player player) {
        if (player != getCurrentPlayer()) {
            return;
        }
        
        player.setHasPassed(true);
        
        // Check if all players have passed
        boolean allPassed = true;
        for (Player p : players) {
            if (!p.hasPassed() && p.canPlay(board.getLeftValue(), board.getRightValue())) {
                allPassed = false;
                break;
            }
        }
        
        if (allPassed) {
            endRoundBlocked();
        } else {
            nextPlayer();
        }
    }
    
    // Getters and utility methods
    public List<Player> getPlayers() { return players; }
    public GameBoard getBoard() { return board; }
    public Player getCurrentPlayer() { return players.get(currentPlayerIndex); }
    public GameState getGameState() { return gameState; }
    public GameMode getGameMode() { return gameMode; }
    public int getMaxScore() { return maxScore; }
    public void setMaxScore(int maxScore) { this.maxScore = maxScore; }
    public boolean isTeamPlay() { return isTeamPlay; }
    public void setTeamPlay(boolean teamPlay) { this.isTeamPlay = teamPlay; }
    public Player getWinner() { return winner; }
    public int getRoundNumber() { return roundNumber; }
    
    private boolean isValidMove(Player player, DominoPiece piece) {
        return player == getCurrentPlayer() && 
               player.getHand().contains(piece) && 
               board.canPlacePiece(piece);
    }
    
    private void nextPlayer() {
        getCurrentPlayer().setMyTurn(false);
        currentPlayerIndex = (currentPlayerIndex + 1) % players.size();
        getCurrentPlayer().setMyTurn(true);
    }
    
    private void endRound(Player winner) {
        gameState = GameState.ROUND_OVER;
        calculateRoundScore(winner);
        checkGameEnd();
    }
    
    private void endRoundBlocked() {
        gameState = GameState.ROUND_OVER;
        Player roundWinner = findPlayerWithLowestHand();
        calculateRoundScore(roundWinner);
        checkGameEnd();
    }
    
    private Player findPlayerWithLowestHand() {
        Player lowest = players.get(0);
        for (Player player : players) {
            if (player.getHandValue() < lowest.getHandValue()) {
                lowest = player;
            }
        }
        return lowest;
    }
    
    private void calculateRoundScore(Player roundWinner) {
        int totalPoints = 0;
        for (Player player : players) {
            if (player != roundWinner) {
                totalPoints += player.getHandValue();
            }
        }
        roundWinner.addScore(totalPoints);
        roundWinner.incrementRoundsWon();
    }
    
    private void checkGameEnd() {
        for (Player player : players) {
            if (player.getScore() >= maxScore) {
                gameState = GameState.GAME_OVER;
                winner = player;
                return;
            }
        }
        
        // Continue to next round
        roundNumber++;
        startNewRound();
    }
}
