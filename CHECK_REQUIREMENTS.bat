@echo off
echo ========================================
echo    فحص متطلبات بناء المشروع
echo    Build Requirements Check
echo ========================================
echo.

echo [1/4] فحص Java...
java -version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Java غير مثبت أو غير موجود في PATH
    echo.
    echo لتثبيت Java:
    echo 1. اذهب إلى: https://www.oracle.com/java/technologies/downloads/
    echo 2. حمل Java 8 أو أحدث
    echo 3. ثبت Java وأضفه إلى PATH
    echo.
    set JAVA_OK=0
) else (
    echo ✅ Java متوفر
    java -version
    set JAVA_OK=1
)

echo.
echo [2/4] فحص Android SDK...
if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo ✅ Android SDK متوفر في: %ANDROID_HOME%
    set SDK_OK=1
) else if exist "%LOCALAPPDATA%\Android\Sdk\platform-tools\adb.exe" (
    echo ✅ Android SDK متوفر في: %LOCALAPPDATA%\Android\Sdk
    set SDK_OK=1
) else (
    echo ❌ Android SDK غير موجود
    echo.
    echo لتثبيت Android SDK:
    echo 1. حمل Android Studio من: https://developer.android.com/studio
    echo 2. ثبت Android Studio
    echo 3. افتح SDK Manager وحمل:
    echo    - Android SDK Platform-Tools
    echo    - Android SDK Build-Tools
    echo    - Android API Level 21+ 
    echo.
    set SDK_OK=0
)

echo.
echo [3/4] فحص Gradle...
gradle -version 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  Gradle غير متوفر، سيتم استخدام Gradle Wrapper
    echo (هذا طبيعي ولا يؤثر على البناء)
    set GRADLE_OK=1
) else (
    echo ✅ Gradle متوفر
    gradle -version | findstr "Gradle"
    set GRADLE_OK=1
)

echo.
echo [4/4] فحص ملفات المشروع...
if exist "build.gradle" (
    echo ✅ build.gradle موجود
    set FILES_OK=1
) else (
    echo ❌ build.gradle غير موجود
    set FILES_OK=0
)

if exist "app\build.gradle" (
    echo ✅ app\build.gradle موجود
) else (
    echo ❌ app\build.gradle غير موجود
    set FILES_OK=0
)

if exist "app\src\main\AndroidManifest.xml" (
    echo ✅ AndroidManifest.xml موجود
) else (
    echo ❌ AndroidManifest.xml غير موجود
    set FILES_OK=0
)

if exist "app\src\main\java\ratmil\domino" (
    echo ✅ ملفات الكود المصدري موجودة
) else (
    echo ❌ ملفات الكود المصدري غير موجودة
    set FILES_OK=0
)

if exist "app\src\main\res\values\strings.xml" (
    echo ✅ ملفات الموارد موجودة
) else (
    echo ❌ ملفات الموارد غير موجودة
    set FILES_OK=0
)

echo.
echo ========================================
echo           نتيجة الفحص
echo ========================================

if %JAVA_OK%==1 if %SDK_OK%==1 if %GRADLE_OK%==1 if %FILES_OK%==1 (
    echo 🎉 جميع المتطلبات متوفرة!
    echo يمكنك الآن بناء المشروع بتشغيل: BUILD_PROJECT.bat
    echo.
    echo أو يدوياً:
    echo 1. cd C:\DominoGame
    echo 2. gradle clean assembleDebug
) else (
    echo ❌ بعض المتطلبات غير متوفرة
    echo يرجى إصلاح المشاكل المذكورة أعلاه قبل البناء
)

echo.
echo ========================================
echo        معلومات إضافية
echo ========================================
echo.
echo 📁 مجلد المشروع الحالي: %CD%
echo 🔧 نظام التشغيل: %OS%
echo 💻 معمارية النظام: %PROCESSOR_ARCHITECTURE%
echo.
echo لمزيد من المساعدة، راجع ملف: BUILD_INSTRUCTIONS.md
echo.
pause
