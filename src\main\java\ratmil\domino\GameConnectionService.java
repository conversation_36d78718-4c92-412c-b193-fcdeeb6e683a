package ratmil.domino;

import android.app.Service;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothServerSocket;
import android.bluetooth.BluetoothSocket;
import android.content.Intent;
import android.os.Binder;
import android.os.Handler;
import android.os.IBinder;
import android.util.Log;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.UUID;

/**
 * Service for managing Bluetooth connections for multiplayer domino game
 */
public class GameConnectionService extends Service {
    
    private static final String TAG = "GameConnectionService";
    private static final String SERVICE_NAME = "DominoGame";
    private static final UUID SERVICE_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
    
    public enum ConnectionState {
        NONE, LISTENING, CONNECTING, CONNECTED
    }
    
    private BluetoothAdapter bluetoothAdapter;
    private AcceptThread acceptThread;
    private ConnectThread connectThread;
    private ConnectedThread connectedThread;
    private ConnectionState state;
    private Handler handler;
    
    // Message types for communication with UI
    public static final int MESSAGE_STATE_CHANGE = 1;
    public static final int MESSAGE_READ = 2;
    public static final int MESSAGE_WRITE = 3;
    public static final int MESSAGE_DEVICE_NAME = 4;
    public static final int MESSAGE_CONNECTION_FAILED = 5;
    public static final int MESSAGE_CONNECTION_LOST = 6;
    
    public static final String DEVICE_NAME = "device_name";
    
    private final IBinder binder = new LocalBinder();
    
    public class LocalBinder extends Binder {
        GameConnectionService getService() {
            return GameConnectionService.this;
        }
    }
    
    @Override
    public void onCreate() {
        super.onCreate();
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        state = ConnectionState.NONE;
        handler = new Handler();
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String connectionType = intent.getStringExtra("connection_type");
            BluetoothDevice device = intent.getParcelableExtra("device");
            
            if ("create".equals(connectionType)) {
                startListening();
            } else if ("join".equals(connectionType) && device != null) {
                connect(device);
            }
        }
        
        return START_STICKY;
    }
    
    /**
     * Start listening for incoming connections
     */
    public synchronized void startListening() {
        Log.d(TAG, "Starting to listen for connections");
        
        // Cancel any existing threads
        if (connectThread != null) {
            connectThread.cancel();
            connectThread = null;
        }
        
        if (connectedThread != null) {
            connectedThread.cancel();
            connectedThread = null;
        }
        
        // Start accept thread
        if (acceptThread == null) {
            acceptThread = new AcceptThread();
            acceptThread.start();
        }
        
        setState(ConnectionState.LISTENING);
    }
    
    /**
     * Connect to a remote device
     */
    public synchronized void connect(BluetoothDevice device) {
        Log.d(TAG, "Connecting to: " + device.getName());
        
        // Cancel any existing threads
        if (state == ConnectionState.CONNECTING && connectThread != null) {
            connectThread.cancel();
            connectThread = null;
        }
        
        if (connectedThread != null) {
            connectedThread.cancel();
            connectedThread = null;
        }
        
        // Start connect thread
        connectThread = new ConnectThread(device);
        connectThread.start();
        setState(ConnectionState.CONNECTING);
    }
    
    /**
     * Manage a connected socket
     */
    public synchronized void connected(BluetoothSocket socket, BluetoothDevice device) {
        Log.d(TAG, "Connected to: " + device.getName());
        
        // Cancel threads
        if (connectThread != null) {
            connectThread.cancel();
            connectThread = null;
        }
        
        if (connectedThread != null) {
            connectedThread.cancel();
            connectedThread = null;
        }
        
        if (acceptThread != null) {
            acceptThread.cancel();
            acceptThread = null;
        }
        
        // Start connected thread
        connectedThread = new ConnectedThread(socket);
        connectedThread.start();
        
        // Send device name to UI
        sendMessage(MESSAGE_DEVICE_NAME, device.getName());
        setState(ConnectionState.CONNECTED);
    }
    
    /**
     * Stop all threads
     */
    public synchronized void stop() {
        Log.d(TAG, "Stopping connection service");
        
        if (connectThread != null) {
            connectThread.cancel();
            connectThread = null;
        }
        
        if (connectedThread != null) {
            connectedThread.cancel();
            connectedThread = null;
        }
        
        if (acceptThread != null) {
            acceptThread.cancel();
            acceptThread = null;
        }
        
        setState(ConnectionState.NONE);
    }
    
    /**
     * Write data to connected device
     */
    public void write(byte[] data) {
        ConnectedThread thread;
        synchronized (this) {
            if (state != ConnectionState.CONNECTED) return;
            thread = connectedThread;
        }
        thread.write(data);
    }
    
    private synchronized void setState(ConnectionState newState) {
        Log.d(TAG, "State change: " + state + " -> " + newState);
        state = newState;
        sendMessage(MESSAGE_STATE_CHANGE, newState.ordinal());
    }
    
    public ConnectionState getState() {
        return state;
    }
    
    private void sendMessage(int what, Object obj) {
        // In a full implementation, this would send messages to bound activities
        // For now, we'll use broadcasts
        Intent intent = new Intent("ratmil.domino.CONNECTION_UPDATE");
        intent.putExtra("message_type", what);
        if (obj instanceof String) {
            intent.putExtra("message_data", (String) obj);
        } else if (obj instanceof Integer) {
            intent.putExtra("message_data", (Integer) obj);
        }
        sendBroadcast(intent);
    }
    
    /**
     * Thread for listening for incoming connections
     */
    private class AcceptThread extends Thread {
        private BluetoothServerSocket serverSocket;
        
        public AcceptThread() {
            try {
                serverSocket = bluetoothAdapter.listenUsingRfcommWithServiceRecord(
                    SERVICE_NAME, SERVICE_UUID);
            } catch (IOException e) {
                Log.e(TAG, "Failed to create server socket", e);
            }
        }
        
        @Override
        public void run() {
            Log.d(TAG, "Accept thread started");
            setName("AcceptThread");
            
            BluetoothSocket socket;
            
            while (state != ConnectionState.CONNECTED) {
                try {
                    socket = serverSocket.accept();
                } catch (IOException e) {
                    Log.e(TAG, "Accept failed", e);
                    break;
                }
                
                if (socket != null) {
                    synchronized (GameConnectionService.this) {
                        switch (state) {
                            case LISTENING:
                            case CONNECTING:
                                connected(socket, socket.getRemoteDevice());
                                break;
                            case NONE:
                            case CONNECTED:
                                try {
                                    socket.close();
                                } catch (IOException e) {
                                    Log.e(TAG, "Could not close socket", e);
                                }
                                break;
                        }
                    }
                }
            }
            
            Log.d(TAG, "Accept thread ended");
        }
        
        public void cancel() {
            Log.d(TAG, "Canceling accept thread");
            try {
                if (serverSocket != null) {
                    serverSocket.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "Failed to close server socket", e);
            }
        }
    }
    
    /**
     * Thread for connecting to a remote device
     */
    private class ConnectThread extends Thread {
        private BluetoothSocket socket;
        private BluetoothDevice device;
        
        public ConnectThread(BluetoothDevice device) {
            this.device = device;
            
            try {
                socket = device.createRfcommSocketToServiceRecord(SERVICE_UUID);
            } catch (IOException e) {
                Log.e(TAG, "Failed to create socket", e);
            }
        }
        
        @Override
        public void run() {
            Log.d(TAG, "Connect thread started");
            setName("ConnectThread");
            
            // Cancel discovery
            bluetoothAdapter.cancelDiscovery();
            
            try {
                socket.connect();
            } catch (IOException e) {
                Log.e(TAG, "Connection failed", e);
                sendMessage(MESSAGE_CONNECTION_FAILED, null);
                
                try {
                    socket.close();
                } catch (IOException e2) {
                    Log.e(TAG, "Failed to close socket", e2);
                }
                return;
            }
            
            synchronized (GameConnectionService.this) {
                connectThread = null;
            }
            
            connected(socket, device);
        }
        
        public void cancel() {
            try {
                if (socket != null) {
                    socket.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "Failed to close socket", e);
            }
        }
    }
    
    /**
     * Thread for managing a connected socket
     */
    private class ConnectedThread extends Thread {
        private BluetoothSocket socket;
        private InputStream inputStream;
        private OutputStream outputStream;
        
        public ConnectedThread(BluetoothSocket socket) {
            this.socket = socket;
            
            try {
                inputStream = socket.getInputStream();
                outputStream = socket.getOutputStream();
            } catch (IOException e) {
                Log.e(TAG, "Failed to get streams", e);
            }
        }
        
        @Override
        public void run() {
            Log.d(TAG, "Connected thread started");
            byte[] buffer = new byte[1024];
            int bytes;
            
            while (true) {
                try {
                    bytes = inputStream.read(buffer);
                    sendMessage(MESSAGE_READ, new String(buffer, 0, bytes));
                } catch (IOException e) {
                    Log.e(TAG, "Connection lost", e);
                    sendMessage(MESSAGE_CONNECTION_LOST, null);
                    break;
                }
            }
        }
        
        public void write(byte[] buffer) {
            try {
                outputStream.write(buffer);
                sendMessage(MESSAGE_WRITE, new String(buffer));
            } catch (IOException e) {
                Log.e(TAG, "Failed to write", e);
            }
        }
        
        public void cancel() {
            try {
                if (socket != null) {
                    socket.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "Failed to close socket", e);
            }
        }
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        stop();
    }
}
