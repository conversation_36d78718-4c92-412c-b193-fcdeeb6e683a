package ratmil.domino.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents the game board where domino pieces are placed
 */
public class GameBoard implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private List<DominoPiece> chain;
    private int leftValue;
    private int rightValue;
    private boolean isEmpty;
    
    public GameBoard() {
        this.chain = new ArrayList<>();
        this.leftValue = -1;
        this.rightValue = -1;
        this.isEmpty = true;
    }
    
    /**
     * Place the first piece on the board
     */
    public boolean placeFirstPiece(DominoPiece piece) {
        if (!isEmpty) {
            return false;
        }
        
        chain.add(piece);
        piece.setPlayed(true);
        
        if (piece.isDouble()) {
            leftValue = piece.getTopValue();
            rightValue = piece.getBottomValue();
        } else {
            leftValue = piece.getTopValue();
            rightValue = piece.getBottomValue();
        }
        
        isEmpty = false;
        return true;
    }
    
    /**
     * Place a piece on the left side of the chain
     */
    public boolean placeOnLeft(DominoPiece piece) {
        if (isEmpty || !piece.canConnectTo(leftValue)) {
            return false;
        }
        
        // Orient the piece correctly
        if (piece.getBottomValue() == leftValue) {
            piece.flip();
        }
        
        chain.add(0, piece);
        piece.setPlayed(true);
        
        // Update left value
        if (piece.getTopValue() == leftValue) {
            leftValue = piece.getBottomValue();
        } else {
            leftValue = piece.getTopValue();
        }
        
        return true;
    }
    
    /**
     * Place a piece on the right side of the chain
     */
    public boolean placeOnRight(DominoPiece piece) {
        if (isEmpty || !piece.canConnectTo(rightValue)) {
            return false;
        }
        
        // Orient the piece correctly
        if (piece.getTopValue() == rightValue) {
            piece.flip();
        }
        
        chain.add(piece);
        piece.setPlayed(true);
        
        // Update right value
        if (piece.getTopValue() == rightValue) {
            rightValue = piece.getBottomValue();
        } else {
            rightValue = piece.getTopValue();
        }
        
        return true;
    }
    
    /**
     * Check if a piece can be placed on the board
     */
    public boolean canPlacePiece(DominoPiece piece) {
        if (isEmpty) {
            return true;
        }
        return piece.canConnectTo(leftValue) || piece.canConnectTo(rightValue);
    }
    
    /**
     * Get the placement options for a piece
     */
    public List<PlacementOption> getPlacementOptions(DominoPiece piece) {
        List<PlacementOption> options = new ArrayList<>();
        
        if (isEmpty) {
            options.add(new PlacementOption(PlacementSide.FIRST, piece));
            return options;
        }
        
        if (piece.canConnectTo(leftValue)) {
            options.add(new PlacementOption(PlacementSide.LEFT, piece));
        }
        
        if (piece.canConnectTo(rightValue)) {
            options.add(new PlacementOption(PlacementSide.RIGHT, piece));
        }
        
        return options;
    }
    
    public List<DominoPiece> getChain() {
        return new ArrayList<>(chain);
    }
    
    public int getLeftValue() {
        return leftValue;
    }
    
    public int getRightValue() {
        return rightValue;
    }
    
    public boolean isEmpty() {
        return isEmpty;
    }
    
    public int getChainLength() {
        return chain.size();
    }
    
    /**
     * Calculate the total value of all pieces on the board
     */
    public int getTotalValue() {
        int total = 0;
        for (DominoPiece piece : chain) {
            total += piece.getTotalValue();
        }
        return total;
    }
    
    /**
     * Check if the board is blocked (no more moves possible)
     */
    public boolean isBlocked(List<Player> players) {
        for (Player player : players) {
            if (player.canPlay(leftValue, rightValue)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Reset the board for a new round
     */
    public void reset() {
        chain.clear();
        leftValue = -1;
        rightValue = -1;
        isEmpty = true;
    }
    
    @Override
    public String toString() {
        if (isEmpty) {
            return "Empty board";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("Board: ");
        for (int i = 0; i < chain.size(); i++) {
            if (i > 0) sb.append(" - ");
            sb.append(chain.get(i).toString());
        }
        sb.append(" (Left: ").append(leftValue).append(", Right: ").append(rightValue).append(")");
        return sb.toString();
    }
    
    /**
     * Represents a placement option for a domino piece
     */
    public static class PlacementOption {
        private final PlacementSide side;
        private final DominoPiece piece;
        
        public PlacementOption(PlacementSide side, DominoPiece piece) {
            this.side = side;
            this.piece = piece;
        }
        
        public PlacementSide getSide() {
            return side;
        }
        
        public DominoPiece getPiece() {
            return piece;
        }
    }
    
    /**
     * Enum for placement sides
     */
    public enum PlacementSide {
        LEFT, RIGHT, FIRST
    }
}
