<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="ratmil.domino"
    android:versionCode="4"
    android:versionName="0.5" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="33" />

    <!-- Bluetooth permissions -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />

    <!-- Bluetooth permissions for Android 12+ -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <!-- Location permission for Bluetooth discovery -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- Bluetooth feature -->
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />

    <permission
        android:name="ratmil.domino.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="ratmil.domino.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme" >

        <!-- Main Activity (Launcher) -->
        <activity
            android:name="ratmil.domino.DominoMainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/AppTheme" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Game Activity -->
        <activity
            android:name="ratmil.domino.DominoActivity"
            android:exported="false"
            android:label="@string/title_activity_domino"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme" />

        <!-- Game Setup Activity -->
        <activity
            android:name="ratmil.domino.DominoGameSetupActivity"
            android:exported="false"
            android:label="@string/game_setup"
            android:theme="@style/AppTheme" />

        <!-- Settings Activity -->
        <activity
            android:name="ratmil.domino.DominoSettingsActivity"
            android:exported="false"
            android:label="@string/settings"
            android:theme="@style/AppTheme" />

        <!-- Connection Activity -->
        <activity
            android:name="ratmil.domino.DominoConnectionActivity"
            android:exported="false"
            android:label="@string/title_activity_connection"
            android:theme="@style/AppTheme" />

        <!-- Debug Activity -->
        <activity
            android:name="ratmil.domino.DebugActivity"
            android:exported="false"
            android:label="@string/title_activity_debug"
            android:theme="@style/AppTheme" />

        <!-- Game Connection Service -->
        <service
            android:name="ratmil.domino.GameConnectionService"
            android:exported="false"
            android:label="@string/network" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="ratmil.domino.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>