<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_home_description_format">%1$s, %2$s</string>
    <string name="abc_action_bar_home_subtitle_description_format">%1$s, %2$s, %3$s</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="about_label">About</string>
    <string name="action_settings">Settings</string>
    <string name="ai1">Hal</string>
    <string name="ai2">Shirka</string>
    <string name="ai3">Eddie</string>
    <string name="app_name">Domino</string>
    <string name="blue">Blue</string>
    <string name="button_new_game">New Game</string>
    <string name="cancel">Cancel</string>
    <string name="clear_debug">Clear</string>
    <string name="close">Close</string>
    <string name="connect">Connect</string>
    <string name="connection_error">Connection error</string>
    <string name="continue_label">Continue</string>
    <string name="create_group">Create Game</string>
    <string name="creator_team">Game Creator Team</string>
    <string name="cuban_domino">Cuban Style Domino Game</string>
    <string name="debug">Debug Data</string>
    <string name="debug_info">Debug Info</string>
    <string name="demo_mode">Demo Mode</string>
    <string name="devices">Devices</string>
    <string name="different_server_version">Different Server Version</string>
    <string name="do_you_want_to_start">Do you want to start?</string>
    <string name="domino_set">Domino Set</string>
    <string name="empty_player_name">[Waiting]</string>
    <string name="error">Error</string>
    <string name="error_bluetooth_not_supported">Error. Bluetooth not supported</string>
    <string name="error_getting_game_data">Error getting game data</string>
    <string name="exit_game_msg">There is a game in progress. Do you want to quit?</string>
    <string name="exit_label">Exit</string>
    <string name="fast">Fast</string>
    <string name="game_creator">Game Creator</string>
    <string name="game_draw">Game is draw</string>
    <string name="game_info">Score</string>
    <string name="game_over">Game Over</string>
    <string name="game_setup">Game Setup</string>
    <string name="getting_game_data">Connecting</string>
    <string name="hello">Hello World, DominoActivity!</string>
    <string name="hello_world">Hello world!</string>
    <string name="i_passed">I pass</string>
    <string name="its_better_if_you_start">"It's better if you start"</string>
    <string name="join_group">Join Game</string>
    <string name="joining_game">Joining</string>
    <string name="let_me_start">Let me start</string>
    <string name="main_title">Domino</string>
    <string name="max_score">Max Score</string>
    <string name="me">Me</string>
    <string name="must_select_device">Select a device to connect</string>
    <string name="network">Network</string>
    <string name="new_game">New Game</string>
    <string name="new_game_label">New Game</string>
    <string name="next_round">Next Round</string>
    <string name="no">No</string>
    <string name="no_devices">No devices</string>
    <string name="no_let_me_start">No, let me start</string>
    <string name="no_way_you_start">No way, you start</string>
    <string name="normal">Normal</string>
    <string name="ok">OK</string>
    <string name="ok_i_will_start">"OK, I'll start"</string>
    <string name="ok_you_start">OK, you start</string>
    <string name="pale">Yellow</string>
    <string name="pieces_28">28 pieces</string>
    <string name="pieces_55">55 pieces</string>
    <string name="player_passed">Player %s passed</string>
    <string name="player_playing">Player %s is playing</string>
    <string name="player_thinking">Player %s is thinking</string>
    <string name="players">Players</string>
    <string name="ready">Touch Start when you are ready</string>
    <string name="ready_to_start">Ready to start</string>
    <string name="retry">Retry</string>
    <string name="round_over">Round Over</string>
    <string name="scanning">Scanning...</string>
    <string name="score">Score</string>
    <string name="settings">Settings</string>
    <string name="slow">Slow</string>
    <string name="speed">Speed</string>
    <string name="start">Start</string>
    <string name="start_game">Start Game</string>
    <string name="team_play">Team Play</string>
    <string name="theme">Theme</string>
    <string name="title_activity_connection">Select Device</string>
    <string name="title_activity_debug">DebugActivity</string>
    <string name="title_activity_domino">Domino</string>
    <string name="unable_to_join">Unable to join game</string>
    <string name="version">Version</string>
    <string name="waiting_for_new_round">Waiting for round to start</string>
    <string name="waiting_for_players">Waiting for players</string>
    <string name="winner_player">Winner: %s</string>
    <string name="yes">Yes</string>
    <string name="your_name">Name</string>
    <string name="your_name_title">Your Name</string>
    <string name="your_partner_doesnt_wants_to_start">"Your partner doesn't want to start"</string>
    <string name="your_partner_wants_to_start">Your partner wants to start</string>
</resources>
