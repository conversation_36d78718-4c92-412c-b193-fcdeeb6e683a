package ratmil.domino;

/**
 * Resource IDs for the Domino game
 * This class is typically auto-generated by Android build tools
 */
public final class R {
    
    public static final class id {
        public static final int player1_score = 0x7f0a0001;
        public static final int player2_score = 0x7f0a0002;
        public static final int game_status = 0x7f0a0003;
        public static final int player_hand = 0x7f0a0004;
        public static final int game_board_view = 0x7f0a0005;
        public static final int pass_button = 0x7f0a0006;
        public static final int menu_button = 0x7f0a0007;
        public static final int start_button = 0x7f0a0008;
        public static final int new_game_button = 0x7f0a0009;
        public static final int join_game_button = 0x7f0a000a;
        public static final int create_game_button = 0x7f0a000b;
        public static final int demo_mode_button = 0x7f0a000c;
        public static final int settings_button = 0x7f0a000d;
        public static final int exit_button = 0x7f0a000e;
        public static final int game_info = 0x7f0a000f;
        public static final int game_settings = 0x7f0a0010;
        public static final int connection_status = 0x7f0a0011;
        public static final int device_name = 0x7f0a0012;
        public static final int progress_bar = 0x7f0a0013;
        public static final int players_list = 0x7f0a0014;
        public static final int start_game_button = 0x7f0a0015;
        public static final int cancel_connection_button = 0x7f0a0016;
        public static final int status_text = 0x7f0a0017;
        public static final int device_list = 0x7f0a0018;
        public static final int scan_button = 0x7f0a0019;
        public static final int make_discoverable_button = 0x7f0a001a;
        public static final int player_name_edit = 0x7f0a001b;
        public static final int difficulty_spinner = 0x7f0a001c;
        public static final int debug_info_text = 0x7f0a001d;
        public static final int clear_button = 0x7f0a001e;
        public static final int retry_button = 0x7f0a001f;
    }
    
    public static final class layout {
        public static final int domino_main_menu = 0x7f0b0001;
        public static final int domino_game_board = 0x7f0b0002;
        public static final int domino_settings = 0x7f0b0003;
        public static final int domino_piece_item = 0x7f0b0004;
        public static final int activity_debug = 0x7f0b0005;
        public static final int domino_game_setup = 0x7f0b0006;
        public static final int domino_connection = 0x7f0b0007;
        public static final int domino_waiting = 0x7f0b0008;
    }
    
    public static final class string {
        public static final int app_name = 0x7f0c0001;
        public static final int me = 0x7f0c0002;
        public static final int ai1 = 0x7f0c0003;
        public static final int ai2 = 0x7f0c0004;
        public static final int ai3 = 0x7f0c0005;
        public static final int error = 0x7f0c0006;
        public static final int ready = 0x7f0c0007;
        public static final int ready_to_start = 0x7f0c0008;
        public static final int player_thinking = 0x7f0c0009;
        public static final int round_over = 0x7f0c000a;
        public static final int game_over = 0x7f0c000b;
        public static final int continue_label = 0x7f0c000c;
        public static final int winner_player = 0x7f0c000d;
        public static final int new_game = 0x7f0c000e;
        public static final int exit_label = 0x7f0c000f;
    }
    
    public static final class menu {
        public static final int domino = 0x7f0d0001;
    }
    
    public static final class drawable {
        public static final int domino_button_background = 0x7f0e0001;
        public static final int domino_card_background = 0x7f0e0002;
        public static final int domino_piece_background = 0x7f0e0003;
        public static final int domino_game_board_background = 0x7f0e0004;
        public static final int domino_player_highlight = 0x7f0e0005;
    }
    
    public static final class anim {
        public static final int domino_piece_flip = 0x7f0f0001;
        public static final int domino_button_press = 0x7f0f0002;
        public static final int domino_slide_in_left = 0x7f0f0003;
        public static final int domino_slide_in_right = 0x7f0f0004;
    }
    
    public static final class color {
        public static final int domino_primary = 0x7f100001;
        public static final int domino_primary_dark = 0x7f100002;
        public static final int domino_accent = 0x7f100003;
        public static final int domino_background = 0x7f100004;
        public static final int domino_surface = 0x7f100005;
        public static final int domino_text_primary = 0x7f100006;
        public static final int domino_text_secondary = 0x7f100007;
        public static final int domino_button_positive = 0x7f100008;
        public static final int domino_button_negative = 0x7f100009;
        public static final int domino_button_neutral = 0x7f10000a;
        public static final int domino_game_board = 0x7f10000b;
        public static final int domino_piece_white = 0x7f10000c;
        public static final int domino_piece_black = 0x7f10000d;
        public static final int domino_score_background = 0x7f10000e;
        public static final int domino_player_highlight = 0x7f10000f;
        public static final int domino_connection_success = 0x7f100010;
        public static final int domino_connection_error = 0x7f100011;
        public static final int domino_waiting = 0x7f100012;
    }
    
    public static final class style {
        public static final int AppTheme = 0x7f110001;
        public static final int DominoButton = 0x7f110002;
        public static final int DominoButton_Positive = 0x7f110003;
        public static final int DominoButton_Negative = 0x7f110004;
        public static final int DominoButton_Neutral = 0x7f110005;
        public static final int DominoText = 0x7f110006;
        public static final int DominoText_Title = 0x7f110007;
        public static final int DominoText_Score = 0x7f110008;
        public static final int DominoCard = 0x7f110009;
    }
}
