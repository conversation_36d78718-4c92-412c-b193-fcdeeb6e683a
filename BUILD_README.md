# 🎮 لعبة الدومينو - دليل البناء السريع

## 🚀 البناء السريع (3 خطوات فقط!)

### 1️⃣ فحص المتطلبات
```bash
# تشغيل فحص المتطلبات
CHECK_REQUIREMENTS.bat
```

### 2️⃣ بناء المشروع
```bash
# تشغيل البناء التلقائي
BUILD_PROJECT.bat
```

### 3️⃣ تثبيت اللعبة
```bash
# تثبيت على الجهاز المتصل
adb install C:\DominoGame\app\build\outputs\apk\debug\app-debug.apk
```

---

## 🎯 ما ستحصل عليه

### ✅ لعبة دومينو كاملة
- **قواعد كوبية أصيلة** مع نظام النقاط الصحيح
- **ذكاء اصطناعي متقدم** بثلاث مستويات صعوبة
- **واجهة عربية كاملة** مع دعم RTL

### 🎮 أوضاع اللعب
- **وضع اللاعب الواحد**: العب ضد 3 خصوم أذكياء
- **وضع اللعب الجماعي**: العب مع الأصدقاء عبر البلوتوث
- **وضع العرض التوضيحي**: شاهد الذكاء الاصطناعي يلعب

### 🎨 التصميم
- **مظاهر متعددة**: 5 مظاهر ألوان مختلفة
- **الوضع الليلي**: تبديل تلقائي حسب النظام
- **رسوم متحركة**: تأثيرات بصرية سلسة

---

## 🛠️ المتطلبات

### الأساسية
- ✅ **Java 8+** - لتشغيل Gradle
- ✅ **Android SDK** - لبناء التطبيق
- ✅ **مساحة 500MB** - للملفات المؤقتة

### الاختيارية
- 🔧 **Android Studio** - للتطوير والتعديل
- 📱 **جهاز Android** - لاختبار اللعبة
- 🌐 **اتصال إنترنت** - لتحميل المكتبات

---

## 🚨 حل المشاكل الشائعة

### ❌ "Java غير موجود"
```bash
# تحميل Java من:
https://www.oracle.com/java/technologies/downloads/

# أو استخدام OpenJDK:
https://adoptium.net/
```

### ❌ "Android SDK غير موجود"
```bash
# تحميل Android Studio:
https://developer.android.com/studio

# أو SDK فقط:
https://developer.android.com/studio#command-tools
```

### ❌ "فشل البناء"
```bash
# تنظيف المشروع
gradle clean

# إعادة البناء
gradle assembleDebug --info
```

### ❌ "مشكلة في المسار"
- تأكد من عدم وجود أحرف خاصة في مسار المشروع
- استخدم مسار قصير مثل `C:\DominoGame`

---

## 📱 تشغيل اللعبة

### على الجهاز
```bash
# تفعيل وضع المطور في الجهاز
# تفعيل USB Debugging
# تثبيت APK
adb install app-debug.apk
```

### على المحاكي
```bash
# تشغيل المحاكي من Android Studio
# سحب وإفلات ملف APK على المحاكي
```

---

## 🎮 كيفية اللعب

### البداية
1. اختر **"لعبة جديدة"** من القائمة الرئيسية
2. حدد مستوى الصعوبة (سهل/متوسط/صعب)
3. اكتب اسمك واضغط **"ابدأ"**

### أثناء اللعب
- **اضغط على قطعة الدومينو** لتحديدها
- **اختر الجانب** (يسار/يمين) لوضع القطعة
- **اضغط "مرر"** إذا لم تستطع اللعب

### الفوز
- **أول لاعب** ينهي قطعه يفوز بالجولة
- **أول لاعب** يصل للنقاط المحددة يفوز باللعبة

---

## 🔧 التخصيص

### تغيير الإعدادات
- **النقاط القصوى**: 50-500 نقطة
- **نوع الدومينو**: 28 أو 55 قطعة
- **السرعة**: بطيء/عادي/سريع
- **المظهر**: 5 مظاهر مختلفة

### اللعب الجماعي
1. لاعب واحد يختار **"إنشاء لعبة"**
2. اللاعبون الآخرون يختارون **"انضمام للعبة"**
3. البحث عن الجهاز والاتصال
4. بدء اللعب!

---

## 📞 الدعم

### مشاكل تقنية
- راجع ملف `BUILD_INSTRUCTIONS.md` للتفاصيل
- تشغيل `CHECK_REQUIREMENTS.bat` للتشخيص

### اقتراحات وتحسينات
- الكود مفتوح المصدر ويمكن تعديله
- جميع الملفات موثقة ومنظمة

---

## 🎉 استمتع باللعب!

**لعبة الدومينو الكوبية** - تجربة أصيلة مع تقنية حديثة! 🎲✨

---

*تم تطوير هذه اللعبة بحب للتراث العربي والتقنية الحديثة* ❤️
