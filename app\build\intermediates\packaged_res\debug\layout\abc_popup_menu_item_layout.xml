<?xml version="1.0" encoding="utf-8"?>
<android.support.v7.internal.view.menu.ListMenuItemView
    android:layout_width="fill_parent"
    android:layout_height="?dropdownListPreferredItemHeight"
    android:minWidth="196.0dip"
    style="@style/RtlOverlay.Widget.AppCompat.PopupMenuItem"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout
        android:layout_gravity="center_vertical"
        android:duplicateParentState="true"
        android:layout_width="0.0dip"
        android:layout_height="wrap_content"
        android:layout_weight="1.0"
        style="@style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup">
        <TextView
            android:textAppearance="?textAppearanceLargePopupMenu"
            android:ellipsize="marquee"
            android:id="@id/title"
            android:fadingEdge="horizontal"
            android:duplicateParentState="true"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_alignParentTop="true"
            style="@style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" />
        <TextView
            android:textAppearance="?textAppearanceSmallPopupMenu"
            android:id="@id/shortcut"
            android:duplicateParentState="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_below="@id/title"
            style="@style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" />
    </RelativeLayout>
</android.support.v7.internal.view.menu.ListMenuItemView>