<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/domino_background">

    <!-- Game Board -->
    <ScrollView
        android:id="@+id/game_board_scroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/player_area"
        android:layout_below="@+id/score_area"
        android:background="@drawable/domino_game_board_background"
        android:layout_margin="8dp">

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/domino_chain"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center" />
        </HorizontalScrollView>
    </ScrollView>

    <!-- Score Area -->
    <LinearLayout
        android:id="@+id/score_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:orientation="horizontal"
        android:background="@drawable/domino_card_background"
        android:padding="8dp"
        android:layout_margin="8dp">

        <TextView
            android:id="@+id/player1_score"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/me"
            style="@style/DominoText.Score"
            android:layout_marginEnd="4dp" />

        <TextView
            android:id="@+id/game_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/ready_to_start"
            style="@style/DominoText"
            android:gravity="center"
            android:layout_marginHorizontal="4dp" />

        <TextView
            android:id="@+id/player2_score"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/ai1"
            style="@style/DominoText.Score"
            android:layout_marginStart="4dp" />
    </LinearLayout>

    <!-- Player Area -->
    <LinearLayout
        android:id="@+id/player_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:background="@drawable/domino_card_background"
        android:padding="8dp"
        android:layout_margin="8dp">

        <!-- Player Hand -->
        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:id="@+id/player_hand"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="8dp" />
        </HorizontalScrollView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/pass_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/i_passed"
                style="@style/DominoButton.Negative"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/menu_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/settings"
                style="@style/DominoButton.Neutral"
                android:layout_marginHorizontal="4dp" />

            <Button
                android:id="@+id/start_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/start"
                style="@style/DominoButton.Positive"
                android:layout_marginStart="4dp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>
