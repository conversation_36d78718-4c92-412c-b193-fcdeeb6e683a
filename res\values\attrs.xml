<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="actionBarDivider" format="reference" />
    <attr name="actionBarItemBackground" format="reference" />
    <attr name="actionBarPopupTheme" format="reference" />
    <attr name="actionBarSize" format="dimension">
        <enum name="wrap_content" value="0" />
    </attr>
    <attr name="actionBarSplitStyle" format="reference" />
    <attr name="actionBarStyle" format="reference" />
    <attr name="actionBarTabBarStyle" format="reference" />
    <attr name="actionBarTabStyle" format="reference" />
    <attr name="actionBarTabTextStyle" format="reference" />
    <attr name="actionBarTheme" format="reference" />
    <attr name="actionBarWidgetTheme" format="reference" />
    <attr name="actionButtonStyle" format="reference" />
    <attr name="actionDropDownStyle" format="reference" />
    <attr name="actionLayout" format="reference" />
    <attr name="actionMenuTextAppearance" format="reference" />
    <attr name="actionMenuTextColor" format="reference|color" />
    <attr name="actionModeBackground" format="reference" />
    <attr name="actionModeCloseButtonStyle" format="reference" />
    <attr name="actionModeCloseDrawable" format="reference" />
    <attr name="actionModeCopyDrawable" format="reference" />
    <attr name="actionModeCutDrawable" format="reference" />
    <attr name="actionModeFindDrawable" format="reference" />
    <attr name="actionModePasteDrawable" format="reference" />
    <attr name="actionModePopupWindowStyle" format="reference" />
    <attr name="actionModeSelectAllDrawable" format="reference" />
    <attr name="actionModeShareDrawable" format="reference" />
    <attr name="actionModeSplitBackground" format="reference" />
    <attr name="actionModeStyle" format="reference" />
    <attr name="actionModeWebSearchDrawable" format="reference" />
    <attr name="actionOverflowButtonStyle" format="reference" />
    <attr name="actionOverflowMenuStyle" format="reference" />
    <attr name="actionProviderClass" format="string" />
    <attr name="actionViewClass" format="string" />
    <attr name="activityChooserViewStyle" format="reference" />
    <attr name="alertDialogButtonGroupStyle" format="reference" />
    <attr name="alertDialogCenterButtons" format="boolean" />
    <attr name="alertDialogStyle" format="reference" />
    <attr name="alertDialogTheme" format="reference" />
    <attr name="autoCompleteTextViewStyle" format="reference" />
    <attr name="background" format="reference" />
    <attr name="backgroundSplit" format="reference|color" />
    <attr name="backgroundStacked" format="reference|color" />
    <attr name="backgroundTint" format="color" />
    <attr name="backgroundTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="barSize" format="dimension" />
    <attr name="buttonBarButtonStyle" format="reference" />
    <attr name="buttonBarNegativeButtonStyle" format="reference" />
    <attr name="buttonBarNeutralButtonStyle" format="reference" />
    <attr name="buttonBarPositiveButtonStyle" format="reference" />
    <attr name="buttonBarStyle" format="reference" />
    <attr name="buttonPanelSideLayout" format="reference" />
    <attr name="buttonStyle" format="reference" />
    <attr name="buttonStyleSmall" format="reference" />
    <attr name="checkboxStyle" format="reference" />
    <attr name="checkedTextViewStyle" format="reference" />
    <attr name="closeIcon" format="reference" />
    <attr name="closeItemLayout" format="reference" />
    <attr name="collapseContentDescription" format="string" />
    <attr name="collapseIcon" format="reference" />
    <attr name="color" format="color" />
    <attr name="colorAccent" format="color" />
    <attr name="colorButtonNormal" format="color" />
    <attr name="colorControlActivated" format="color" />
    <attr name="colorControlHighlight" format="color" />
    <attr name="colorControlNormal" format="color" />
    <attr name="colorPrimary" format="color" />
    <attr name="colorPrimaryDark" format="color" />
    <attr name="colorSwitchThumbNormal" format="color" />
    <attr name="commitIcon" format="reference" />
    <attr name="contentInsetEnd" format="dimension" />
    <attr name="contentInsetLeft" format="dimension" />
    <attr name="contentInsetRight" format="dimension" />
    <attr name="contentInsetStart" format="dimension" />
    <attr name="customNavigationLayout" format="reference" />
    <attr name="dialogPreferredPadding" format="dimension" />
    <attr name="dialogTheme" format="reference" />
    <attr name="disableChildrenWhenDisabled" format="boolean" />
    <attr name="displayOptions">
        <flag name="disableHome" value="0x00000020" />
        <flag name="homeAsUp" value="0x00000004" />
        <flag name="none" value="0x00000000" />
        <flag name="showCustom" value="0x00000010" />
        <flag name="showHome" value="0x00000002" />
        <flag name="showTitle" value="0x00000008" />
        <flag name="useLogo" value="0x00000001" />
    </attr>
    <attr name="divider" format="reference" />
    <attr name="dividerHorizontal" format="reference" />
    <attr name="dividerPadding" format="dimension" />
    <attr name="dividerVertical" format="reference" />
    <attr name="drawableSize" format="dimension" />
    <attr name="drawerArrowStyle" format="reference" />
    <attr name="dropDownListViewStyle" format="reference" />
    <attr name="dropdownListPreferredItemHeight" format="dimension" />
    <attr name="editTextBackground" format="reference" />
    <attr name="editTextColor" format="reference|color" />
    <attr name="editTextStyle" format="reference" />
    <attr name="elevation" format="dimension" />
    <attr name="expandActivityOverflowButtonDrawable" format="reference" />
    <attr name="gapBetweenBars" format="dimension" />
    <attr name="goIcon" format="reference" />
    <attr name="height" format="dimension" />
    <attr name="hideOnContentScroll" format="boolean" />
    <attr name="homeAsUpIndicator" format="reference" />
    <attr name="homeLayout" format="reference" />
    <attr name="icon" format="reference" />
    <attr name="iconifiedByDefault" format="boolean" />
    <attr name="indeterminateProgressStyle" format="reference" />
    <attr name="initialActivityCount" format="string" />
    <attr name="isLightTheme" format="boolean" />
    <attr name="itemPadding" format="dimension" />
    <attr name="layout" format="reference" />
    <attr name="listChoiceBackgroundIndicator" format="reference" />
    <attr name="listDividerAlertDialog" format="reference" />
    <attr name="listItemLayout" format="reference" />
    <attr name="listLayout" format="reference" />
    <attr name="listPopupWindowStyle" format="reference" />
    <attr name="listPreferredItemHeight" format="dimension" />
    <attr name="listPreferredItemHeightLarge" format="dimension" />
    <attr name="listPreferredItemHeightSmall" format="dimension" />
    <attr name="listPreferredItemPaddingLeft" format="dimension" />
    <attr name="listPreferredItemPaddingRight" format="dimension" />
    <attr name="logo" format="reference" />
    <attr name="maxButtonHeight" format="dimension" />
    <attr name="measureWithLargestChild" format="boolean" />
    <attr name="middleBarArrowSize" format="dimension" />
    <attr name="multiChoiceItemLayout" format="reference" />
    <attr name="navigationContentDescription" format="string" />
    <attr name="navigationIcon" format="reference" />
    <attr name="navigationMode">
        <enum name="listMode" value="1" />
        <enum name="normal" value="0" />
        <enum name="tabMode" value="2" />
    </attr>
    <attr name="overlapAnchor" format="boolean" />
    <attr name="paddingEnd" format="dimension" />
    <attr name="paddingStart" format="dimension" />
    <attr name="panelBackground" format="reference" />
    <attr name="panelMenuListTheme" format="reference" />
    <attr name="panelMenuListWidth" format="dimension" />
    <attr name="popupMenuStyle" format="reference" />
    <attr name="popupPromptView" format="reference" />
    <attr name="popupTheme" format="reference" />
    <attr name="popupWindowStyle" format="reference" />
    <attr name="preserveIconSpacing" format="boolean" />
    <attr name="progressBarPadding" format="dimension" />
    <attr name="progressBarStyle" format="reference" />
    <attr name="prompt" format="reference" />
    <attr name="queryBackground" format="reference" />
    <attr name="queryHint" format="string" />
    <attr name="radioButtonStyle" format="reference" />
    <attr name="ratingBarStyle" format="reference" />
    <attr name="searchHintIcon" format="reference" />
    <attr name="searchIcon" format="reference" />
    <attr name="searchViewStyle" format="reference" />
    <attr name="selectableItemBackground" format="reference" />
    <attr name="selectableItemBackgroundBorderless" format="reference" />
    <attr name="showAsAction">
        <flag name="always" value="0x00000002" />
        <flag name="collapseActionView" value="0x00000008" />
        <flag name="ifRoom" value="0x00000001" />
        <flag name="never" value="0x00000000" />
        <flag name="withText" value="0x00000004" />
    </attr>
    <attr name="showDividers">
        <flag name="beginning" value="0x00000001" />
        <flag name="end" value="0x00000004" />
        <flag name="middle" value="0x00000002" />
        <flag name="none" value="0x00000000" />
    </attr>
    <attr name="showText" format="boolean" />
    <attr name="singleChoiceItemLayout" format="reference" />
    <attr name="spinBars" format="boolean" />
    <attr name="spinnerDropDownItemStyle" format="reference" />
    <attr name="spinnerMode">
        <enum name="dialog" value="0" />
        <enum name="dropdown" value="1" />
    </attr>
    <attr name="spinnerStyle" format="reference" />
    <attr name="splitTrack" format="boolean" />
    <attr name="state_above_anchor" format="boolean" />
    <attr name="submitBackground" format="reference" />
    <attr name="subtitle" format="string" />
    <attr name="subtitleTextAppearance" format="reference" />
    <attr name="subtitleTextStyle" format="reference" />
    <attr name="suggestionRowLayout" format="reference" />
    <attr name="switchMinWidth" format="dimension" />
    <attr name="switchPadding" format="dimension" />
    <attr name="switchStyle" format="reference" />
    <attr name="switchTextAppearance" format="reference" />
    <attr name="textAllCaps" format="reference|boolean" />
    <attr name="textAppearanceLargePopupMenu" format="reference" />
    <attr name="textAppearanceListItem" format="reference" />
    <attr name="textAppearanceListItemSmall" format="reference" />
    <attr name="textAppearanceSearchResultSubtitle" format="reference" />
    <attr name="textAppearanceSearchResultTitle" format="reference" />
    <attr name="textAppearanceSmallPopupMenu" format="reference" />
    <attr name="textColorAlertDialogListItem" format="reference|color" />
    <attr name="textColorSearchUrl" format="reference|color" />
    <attr name="theme" format="reference" />
    <attr name="thickness" format="dimension" />
    <attr name="thumbTextPadding" format="dimension" />
    <attr name="title" format="string" />
    <attr name="titleMarginBottom" format="dimension" />
    <attr name="titleMarginEnd" format="dimension" />
    <attr name="titleMarginStart" format="dimension" />
    <attr name="titleMarginTop" format="dimension" />
    <attr name="titleMargins" format="dimension" />
    <attr name="titleTextAppearance" format="reference" />
    <attr name="titleTextStyle" format="reference" />
    <attr name="toolbarNavigationButtonStyle" format="reference" />
    <attr name="toolbarStyle" format="reference" />
    <attr name="topBottomBarArrowSize" format="dimension" />
    <attr name="track" format="reference" />
    <attr name="voiceIcon" format="reference" />
    <attr name="windowActionBar" format="boolean" />
    <attr name="windowActionBarOverlay" format="boolean" />
    <attr name="windowActionModeOverlay" format="boolean" />
    <attr name="windowFixedHeightMajor" format="dimension|fraction" />
    <attr name="windowFixedHeightMinor" format="dimension|fraction" />
    <attr name="windowFixedWidthMajor" format="dimension|fraction" />
    <attr name="windowFixedWidthMinor" format="dimension|fraction" />
    <attr name="windowMinWidthMajor" format="dimension|fraction" />
    <attr name="windowMinWidthMinor" format="dimension|fraction" />
    <attr name="windowNoTitle" format="boolean" />
</resources>
