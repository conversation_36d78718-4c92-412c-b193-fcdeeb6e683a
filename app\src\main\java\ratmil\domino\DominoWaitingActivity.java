package ratmil.domino;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

/**
 * Activity shown while waiting for multiplayer connection
 */
public class DominoWaitingActivity extends Activity {
    
    private TextView connectionStatus;
    private TextView deviceName;
    private ProgressBar progressBar;
    private LinearLayout playersList;
    private Button startGameButton;
    private Button cancelButton;
    
    private String connectionType;
    private boolean isConnected = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.domino_waiting);
        
        connectionType = getIntent().getStringExtra("connection_type");
        String deviceNameStr = getIntent().getStringExtra("device_name");
        
        initializeViews();
        setupClickListeners();
        
        if (deviceNameStr != null) {
            deviceName.setText(deviceNameStr);
        }
        
        updateUI();
        
        // Register for connection updates
        IntentFilter filter = new IntentFilter("ratmil.domino.CONNECTION_UPDATE");
        registerReceiver(connectionReceiver, filter);
    }
    
    private void initializeViews() {
        connectionStatus = findViewById(R.id.connection_status);
        deviceName = findViewById(R.id.device_name);
        progressBar = findViewById(R.id.progress_bar);
        playersList = findViewById(R.id.players_list);
        startGameButton = findViewById(R.id.start_game_button);
        cancelButton = findViewById(R.id.cancel_connection_button);
    }
    
    private void setupClickListeners() {
        startGameButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startMultiplayerGame();
            }
        });
        
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cancelConnection();
            }
        });
    }
    
    private void updateUI() {
        if ("create".equals(connectionType)) {
            connectionStatus.setText(R.string.waiting_for_players);
            startGameButton.setVisibility(isConnected ? View.VISIBLE : View.GONE);
        } else {
            connectionStatus.setText(R.string.joining_game);
            startGameButton.setVisibility(View.GONE);
        }
        
        progressBar.setVisibility(isConnected ? View.GONE : View.VISIBLE);
    }
    
    private void startMultiplayerGame() {
        // Start the actual game
        Intent intent = new Intent(this, DominoActivity.class);
        intent.putExtra("game_mode", "MULTIPLAYER_BLUETOOTH");
        intent.putExtra("is_host", "create".equals(connectionType));
        startActivity(intent);
        finish();
    }
    
    private void cancelConnection() {
        // Stop the connection service
        Intent serviceIntent = new Intent(this, GameConnectionService.class);
        stopService(serviceIntent);
        
        finish();
    }
    
    private void addPlayerToList(String playerName) {
        TextView playerView = new TextView(this);
        playerView.setText("• " + playerName);
        playerView.setTextSize(16);
        playerView.setPadding(16, 8, 16, 8);
        playersList.addView(playerView);
    }
    
    private final BroadcastReceiver connectionReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            int messageType = intent.getIntExtra("message_type", -1);
            
            switch (messageType) {
                case GameConnectionService.MESSAGE_STATE_CHANGE:
                    int state = intent.getIntExtra("message_data", 0);
                    handleStateChange(state);
                    break;
                    
                case GameConnectionService.MESSAGE_DEVICE_NAME:
                    String name = intent.getStringExtra("message_data");
                    if (name != null) {
                        deviceName.setText(name);
                        addPlayerToList(name);
                    }
                    break;
                    
                case GameConnectionService.MESSAGE_CONNECTION_FAILED:
                    connectionStatus.setText(R.string.connection_error);
                    progressBar.setVisibility(View.GONE);
                    break;
                    
                case GameConnectionService.MESSAGE_CONNECTION_LOST:
                    connectionStatus.setText(R.string.connection_error);
                    isConnected = false;
                    updateUI();
                    break;
                    
                case GameConnectionService.MESSAGE_READ:
                    String message = intent.getStringExtra("message_data");
                    handleGameMessage(message);
                    break;
            }
        }
    };
    
    private void handleStateChange(int state) {
        GameConnectionService.ConnectionState connectionState = 
            GameConnectionService.ConnectionState.values()[state];
            
        switch (connectionState) {
            case LISTENING:
                connectionStatus.setText(R.string.waiting_for_players);
                break;
            case CONNECTING:
                connectionStatus.setText(R.string.getting_game_data);
                break;
            case CONNECTED:
                connectionStatus.setText(R.string.ready_to_start);
                isConnected = true;
                updateUI();
                break;
            case NONE:
                connectionStatus.setText(R.string.connection_error);
                progressBar.setVisibility(View.GONE);
                break;
        }
    }
    
    private void handleGameMessage(String message) {
        // Handle game-specific messages
        // This could include player join/leave notifications, game start signals, etc.
        if ("PLAYER_JOINED".equals(message)) {
            // Add player to list
        } else if ("GAME_START".equals(message)) {
            startMultiplayerGame();
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        try {
            unregisterReceiver(connectionReceiver);
        } catch (IllegalArgumentException e) {
            // Receiver not registered
        }
    }
    
    @Override
    public void onBackPressed() {
        cancelConnection();
    }
}
