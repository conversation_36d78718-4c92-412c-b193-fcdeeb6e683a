package ratmil.domino.ai;

import ratmil.domino.model.DominoPiece;
import ratmil.domino.model.GameBoard;
import ratmil.domino.model.Player;
import ratmil.domino.model.DominoGame;

import java.util.List;
import java.util.Random;

/**
 * AI logic for domino game with different difficulty levels
 */
public class DominoAI {
    
    private Random random;
    
    public DominoAI() {
        this.random = new Random();
    }
    
    /**
     * Make a move for an AI player
     */
    public AIMove makeMove(Player aiPlayer, GameBoard board, List<Player> allPlayers) {
        List<DominoPiece> playablePieces = aiPlayer.getPlayablePieces(
            board.getLeftValue(), board.getRightValue());
        
        if (playablePieces.isEmpty()) {
            return new AIMove(AIMove.MoveType.PASS, null, null);
        }
        
        switch (aiPlayer.getType()) {
            case AI_EASY:
                return makeEasyMove(playablePieces, board);
            case AI_MEDIUM:
                return makeMediumMove(aiPlayer, playablePieces, board, allPlayers);
            case AI_HARD:
                return makeHardMove(aiPlayer, playablePieces, board, allPlayers);
            default:
                return makeEasyMove(playablePieces, board);
        }
    }
    
    /**
     * Easy AI - Random move
     */
    private AIMove makeEasyMove(List<DominoPiece> playablePieces, GameBoard board) {
        DominoPiece piece = playablePieces.get(random.nextInt(playablePieces.size()));
        GameBoard.PlacementSide side = chooseSide(piece, board);
        return new AIMove(AIMove.MoveType.PLAY, piece, side);
    }
    
    /**
     * Medium AI - Basic strategy
     */
    private AIMove makeMediumMove(Player aiPlayer, List<DominoPiece> playablePieces, 
                                  GameBoard board, List<Player> allPlayers) {
        
        // Priority 1: Play doubles first (Cuban strategy)
        for (DominoPiece piece : playablePieces) {
            if (piece.isDouble()) {
                GameBoard.PlacementSide side = chooseSide(piece, board);
                return new AIMove(AIMove.MoveType.PLAY, piece, side);
            }
        }
        
        // Priority 2: Play highest value pieces
        DominoPiece bestPiece = null;
        int highestValue = -1;
        
        for (DominoPiece piece : playablePieces) {
            if (piece.getTotalValue() > highestValue) {
                highestValue = piece.getTotalValue();
                bestPiece = piece;
            }
        }
        
        GameBoard.PlacementSide side = chooseSide(bestPiece, board);
        return new AIMove(AIMove.MoveType.PLAY, bestPiece, side);
    }
    
    /**
     * Hard AI - Advanced strategy
     */
    private AIMove makeHardMove(Player aiPlayer, List<DominoPiece> playablePieces, 
                                GameBoard board, List<Player> allPlayers) {
        
        // Priority 1: Block opponents if they have few pieces
        Player opponent = findPlayerWithFewestPieces(allPlayers, aiPlayer);
        if (opponent.getHandSize() <= 3) {
            DominoPiece blockingPiece = findBlockingPiece(playablePieces, board, opponent);
            if (blockingPiece != null) {
                GameBoard.PlacementSide side = chooseBestSideForBlocking(blockingPiece, board, opponent);
                return new AIMove(AIMove.MoveType.PLAY, blockingPiece, side);
            }
        }
        
        // Priority 2: Play doubles strategically
        for (DominoPiece piece : playablePieces) {
            if (piece.isDouble() && shouldPlayDouble(piece, aiPlayer, board)) {
                GameBoard.PlacementSide side = chooseSide(piece, board);
                return new AIMove(AIMove.MoveType.PLAY, piece, side);
            }
        }
        
        // Priority 3: Control the board by playing pieces that give us more options
        DominoPiece bestPiece = findBestStrategicPiece(aiPlayer, playablePieces, board);
        GameBoard.PlacementSide side = chooseBestSide(bestPiece, board, aiPlayer);
        
        return new AIMove(AIMove.MoveType.PLAY, bestPiece, side);
    }
    
    /**
     * Choose which side to play a piece on
     */
    private GameBoard.PlacementSide chooseSide(DominoPiece piece, GameBoard board) {
        if (board.isEmpty()) {
            return GameBoard.PlacementSide.FIRST;
        }
        
        boolean canPlayLeft = piece.canConnectTo(board.getLeftValue());
        boolean canPlayRight = piece.canConnectTo(board.getRightValue());
        
        if (canPlayLeft && canPlayRight) {
            // Choose randomly if both sides are available
            return random.nextBoolean() ? GameBoard.PlacementSide.LEFT : GameBoard.PlacementSide.RIGHT;
        } else if (canPlayLeft) {
            return GameBoard.PlacementSide.LEFT;
        } else {
            return GameBoard.PlacementSide.RIGHT;
        }
    }
    
    /**
     * Choose the best side strategically
     */
    private GameBoard.PlacementSide chooseBestSide(DominoPiece piece, GameBoard board, Player aiPlayer) {
        if (board.isEmpty()) {
            return GameBoard.PlacementSide.FIRST;
        }
        
        boolean canPlayLeft = piece.canConnectTo(board.getLeftValue());
        boolean canPlayRight = piece.canConnectTo(board.getRightValue());
        
        if (canPlayLeft && canPlayRight) {
            // Choose side that gives us more future options
            int leftOptions = countFutureOptions(aiPlayer, piece.getOtherValue(board.getLeftValue()));
            int rightOptions = countFutureOptions(aiPlayer, piece.getOtherValue(board.getRightValue()));
            
            return leftOptions >= rightOptions ? GameBoard.PlacementSide.LEFT : GameBoard.PlacementSide.RIGHT;
        } else if (canPlayLeft) {
            return GameBoard.PlacementSide.LEFT;
        } else {
            return GameBoard.PlacementSide.RIGHT;
        }
    }
    
    /**
     * Count how many pieces in hand can connect to a specific value
     */
    private int countFutureOptions(Player player, int value) {
        int count = 0;
        for (DominoPiece piece : player.getHand()) {
            if (piece.canConnectTo(value)) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * Find player with fewest pieces (excluding AI player)
     */
    private Player findPlayerWithFewestPieces(List<Player> players, Player excludePlayer) {
        Player fewest = null;
        int minPieces = Integer.MAX_VALUE;
        
        for (Player player : players) {
            if (player != excludePlayer && player.getHandSize() < minPieces) {
                minPieces = player.getHandSize();
                fewest = player;
            }
        }
        
        return fewest;
    }
    
    /**
     * Find a piece that could block an opponent
     */
    private DominoPiece findBlockingPiece(List<DominoPiece> playablePieces, GameBoard board, Player opponent) {
        // Look for pieces that would create values the opponent can't play
        for (DominoPiece piece : playablePieces) {
            if (wouldBlockOpponent(piece, board, opponent)) {
                return piece;
            }
        }
        return null;
    }
    
    /**
     * Check if playing a piece would block an opponent
     */
    private boolean wouldBlockOpponent(DominoPiece piece, GameBoard board, Player opponent) {
        // Simulate playing the piece and check if opponent can still play
        int newLeftValue = board.getLeftValue();
        int newRightValue = board.getRightValue();
        
        if (piece.canConnectTo(board.getLeftValue())) {
            newLeftValue = piece.getOtherValue(board.getLeftValue());
        }
        if (piece.canConnectTo(board.getRightValue())) {
            newRightValue = piece.getOtherValue(board.getRightValue());
        }
        
        return !opponent.canPlay(newLeftValue, newRightValue);
    }
    
    /**
     * Determine if we should play a double piece
     */
    private boolean shouldPlayDouble(DominoPiece doublePiece, Player aiPlayer, GameBoard board) {
        // Play doubles early to avoid being stuck with them
        return aiPlayer.getHandSize() > 4 || countFutureOptions(aiPlayer, doublePiece.getTopValue()) > 2;
    }
    
    /**
     * Find the best strategic piece to play
     */
    private DominoPiece findBestStrategicPiece(Player aiPlayer, List<DominoPiece> playablePieces, GameBoard board) {
        DominoPiece bestPiece = playablePieces.get(0);
        int bestScore = evaluatePiece(bestPiece, aiPlayer, board);
        
        for (DominoPiece piece : playablePieces) {
            int score = evaluatePiece(piece, aiPlayer, board);
            if (score > bestScore) {
                bestScore = score;
                bestPiece = piece;
            }
        }
        
        return bestPiece;
    }
    
    /**
     * Evaluate how good a piece is to play
     */
    private int evaluatePiece(DominoPiece piece, Player aiPlayer, GameBoard board) {
        int score = 0;
        
        // Higher value pieces are generally better to play early
        score += piece.getTotalValue();
        
        // Doubles get bonus points
        if (piece.isDouble()) {
            score += 5;
        }
        
        // Pieces that give us more future options are better
        if (piece.canConnectTo(board.getLeftValue())) {
            score += countFutureOptions(aiPlayer, piece.getOtherValue(board.getLeftValue()));
        }
        if (piece.canConnectTo(board.getRightValue())) {
            score += countFutureOptions(aiPlayer, piece.getOtherValue(board.getRightValue()));
        }
        
        return score;
    }
    
    private GameBoard.PlacementSide chooseBestSideForBlocking(DominoPiece piece, GameBoard board, Player opponent) {
        // Choose the side that blocks the opponent more effectively
        return chooseSide(piece, board); // Simplified for now
    }
    
    /**
     * Represents an AI move decision
     */
    public static class AIMove {
        public enum MoveType {
            PLAY, PASS
        }
        
        private final MoveType moveType;
        private final DominoPiece piece;
        private final GameBoard.PlacementSide side;
        
        public AIMove(MoveType moveType, DominoPiece piece, GameBoard.PlacementSide side) {
            this.moveType = moveType;
            this.piece = piece;
            this.side = side;
        }
        
        public MoveType getMoveType() { return moveType; }
        public DominoPiece getPiece() { return piece; }
        public GameBoard.PlacementSide getSide() { return side; }
    }
}
