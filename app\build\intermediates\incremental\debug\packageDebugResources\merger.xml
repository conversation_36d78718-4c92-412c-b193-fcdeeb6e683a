<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res"/><source path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\build\generated\res\rs\debug"/><source path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res"><file name="abc_fade_in" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_fade_in.xml" qualifiers="" type="anim"/><file name="abc_fade_out" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_fade_out.xml" qualifiers="" type="anim"/><file name="abc_grow_fade_in_from_bottom" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_grow_fade_in_from_bottom.xml" qualifiers="" type="anim"/><file name="abc_popup_enter" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_popup_enter.xml" qualifiers="" type="anim"/><file name="abc_popup_exit" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_popup_exit.xml" qualifiers="" type="anim"/><file name="abc_shrink_fade_out_from_bottom" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_shrink_fade_out_from_bottom.xml" qualifiers="" type="anim"/><file name="abc_slide_in_bottom" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_slide_in_bottom.xml" qualifiers="" type="anim"/><file name="abc_slide_in_top" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_slide_in_top.xml" qualifiers="" type="anim"/><file name="abc_slide_out_bottom" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_slide_out_bottom.xml" qualifiers="" type="anim"/><file name="abc_slide_out_top" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\abc_slide_out_top.xml" qualifiers="" type="anim"/><file name="domino_button_press" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\domino_button_press.xml" qualifiers="" type="anim"/><file name="domino_piece_flip" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\domino_piece_flip.xml" qualifiers="" type="anim"/><file name="domino_slide_in_left" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\domino_slide_in_left.xml" qualifiers="" type="anim"/><file name="domino_slide_in_right" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\anim\domino_slide_in_right.xml" qualifiers="" type="anim"/><file name="abc_background_cache_hint_selector_material_dark" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\abc_background_cache_hint_selector_material_dark.xml" qualifiers="" type="color"/><file name="abc_background_cache_hint_selector_material_light" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\abc_background_cache_hint_selector_material_light.xml" qualifiers="" type="color"/><file name="abc_primary_text_disable_only_material_dark" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\abc_primary_text_disable_only_material_dark.xml" qualifiers="" type="color"/><file name="abc_primary_text_disable_only_material_light" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\abc_primary_text_disable_only_material_light.xml" qualifiers="" type="color"/><file name="abc_primary_text_material_dark" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\abc_primary_text_material_dark.xml" qualifiers="" type="color"/><file name="abc_primary_text_material_light" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\abc_primary_text_material_light.xml" qualifiers="" type="color"/><file name="abc_search_url_text" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\abc_search_url_text.xml" qualifiers="" type="color"/><file name="abc_secondary_text_material_dark" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\abc_secondary_text_material_dark.xml" qualifiers="" type="color"/><file name="abc_secondary_text_material_light" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\abc_secondary_text_material_light.xml" qualifiers="" type="color"/><file name="switch_thumb_material_dark" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\switch_thumb_material_dark.xml" qualifiers="" type="color"/><file name="switch_thumb_material_light" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\color\switch_thumb_material_light.xml" qualifiers="" type="color"/><file name="abc_btn_borderless_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_btn_borderless_material.xml" qualifiers="" type="drawable"/><file name="abc_btn_check_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_btn_check_material.xml" qualifiers="" type="drawable"/><file name="abc_btn_default_mtrl_shape" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_btn_default_mtrl_shape.xml" qualifiers="" type="drawable"/><file name="abc_btn_radio_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_btn_radio_material.xml" qualifiers="" type="drawable"/><file name="abc_cab_background_internal_bg" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_cab_background_internal_bg.xml" qualifiers="" type="drawable"/><file name="abc_cab_background_top_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_cab_background_top_material.xml" qualifiers="" type="drawable"/><file name="abc_dialog_material_background_dark" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_dialog_material_background_dark.xml" qualifiers="" type="drawable"/><file name="abc_dialog_material_background_light" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_dialog_material_background_light.xml" qualifiers="" type="drawable"/><file name="abc_edit_text_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_edit_text_material.xml" qualifiers="" type="drawable"/><file name="abc_item_background_holo_dark" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_item_background_holo_dark.xml" qualifiers="" type="drawable"/><file name="abc_item_background_holo_light" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_item_background_holo_light.xml" qualifiers="" type="drawable"/><file name="abc_list_selector_background_transition_holo_dark" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_list_selector_background_transition_holo_dark.xml" qualifiers="" type="drawable"/><file name="abc_list_selector_background_transition_holo_light" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_list_selector_background_transition_holo_light.xml" qualifiers="" type="drawable"/><file name="abc_list_selector_holo_dark" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_list_selector_holo_dark.xml" qualifiers="" type="drawable"/><file name="abc_list_selector_holo_light" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_list_selector_holo_light.xml" qualifiers="" type="drawable"/><file name="abc_ratingbar_full_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_ratingbar_full_material.xml" qualifiers="" type="drawable"/><file name="abc_spinner_textfield_background_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_spinner_textfield_background_material.xml" qualifiers="" type="drawable"/><file name="abc_switch_thumb_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_switch_thumb_material.xml" qualifiers="" type="drawable"/><file name="abc_tab_indicator_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_tab_indicator_material.xml" qualifiers="" type="drawable"/><file name="abc_textfield_search_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\abc_textfield_search_material.xml" qualifiers="" type="drawable"/><file name="domino_button_background" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\domino_button_background.xml" qualifiers="" type="drawable"/><file name="domino_card_background" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\domino_card_background.xml" qualifiers="" type="drawable"/><file name="domino_game_board_background" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\domino_game_board_background.xml" qualifiers="" type="drawable"/><file name="domino_piece_background" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\domino_piece_background.xml" qualifiers="" type="drawable"/><file name="domino_player_highlight" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\drawable\domino_player_highlight.xml" qualifiers="" type="drawable"/><file name="abc_action_bar_title_item" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_action_bar_title_item.xml" qualifiers="" type="layout"/><file name="abc_action_bar_up_container" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_action_bar_up_container.xml" qualifiers="" type="layout"/><file name="abc_action_bar_view_list_nav_layout" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_action_bar_view_list_nav_layout.xml" qualifiers="" type="layout"/><file name="abc_action_menu_item_layout" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_action_menu_item_layout.xml" qualifiers="" type="layout"/><file name="abc_action_menu_layout" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_action_menu_layout.xml" qualifiers="" type="layout"/><file name="abc_action_mode_bar" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_action_mode_bar.xml" qualifiers="" type="layout"/><file name="abc_action_mode_close_item_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_action_mode_close_item_material.xml" qualifiers="" type="layout"/><file name="abc_activity_chooser_view" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_activity_chooser_view.xml" qualifiers="" type="layout"/><file name="abc_activity_chooser_view_list_item" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_activity_chooser_view_list_item.xml" qualifiers="" type="layout"/><file name="abc_alert_dialog_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_alert_dialog_material.xml" qualifiers="" type="layout"/><file name="abc_dialog_title_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_dialog_title_material.xml" qualifiers="" type="layout"/><file name="abc_expanded_menu_layout" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_expanded_menu_layout.xml" qualifiers="" type="layout"/><file name="abc_list_menu_item_checkbox" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_list_menu_item_checkbox.xml" qualifiers="" type="layout"/><file name="abc_list_menu_item_icon" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_list_menu_item_icon.xml" qualifiers="" type="layout"/><file name="abc_list_menu_item_layout" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_list_menu_item_layout.xml" qualifiers="" type="layout"/><file name="abc_list_menu_item_radio" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_list_menu_item_radio.xml" qualifiers="" type="layout"/><file name="abc_popup_menu_item_layout" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_popup_menu_item_layout.xml" qualifiers="" type="layout"/><file name="abc_screen_content_include" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_screen_content_include.xml" qualifiers="" type="layout"/><file name="abc_screen_simple" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_screen_simple.xml" qualifiers="" type="layout"/><file name="abc_screen_simple_overlay_action_mode" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_screen_simple_overlay_action_mode.xml" qualifiers="" type="layout"/><file name="abc_screen_toolbar" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_screen_toolbar.xml" qualifiers="" type="layout"/><file name="abc_search_dropdown_item_icons_2line" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_search_dropdown_item_icons_2line.xml" qualifiers="" type="layout"/><file name="abc_search_view" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_search_view.xml" qualifiers="" type="layout"/><file name="abc_select_dialog_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_select_dialog_material.xml" qualifiers="" type="layout"/><file name="abc_simple_dropdown_hint" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\abc_simple_dropdown_hint.xml" qualifiers="" type="layout"/><file name="activity_debug" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\activity_debug.xml" qualifiers="" type="layout"/><file name="domino_connection" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\domino_connection.xml" qualifiers="" type="layout"/><file name="domino_game_board" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\domino_game_board.xml" qualifiers="" type="layout"/><file name="domino_game_setup" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\domino_game_setup.xml" qualifiers="" type="layout"/><file name="domino_main_menu" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\domino_main_menu.xml" qualifiers="" type="layout"/><file name="domino_piece_item" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\domino_piece_item.xml" qualifiers="" type="layout"/><file name="domino_settings" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\domino_settings.xml" qualifiers="" type="layout"/><file name="domino_waiting" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\domino_waiting.xml" qualifiers="" type="layout"/><file name="list_item" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\list_item.xml" qualifiers="" type="layout"/><file name="select_dialog_item_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\select_dialog_item_material.xml" qualifiers="" type="layout"/><file name="select_dialog_multichoice_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\select_dialog_multichoice_material.xml" qualifiers="" type="layout"/><file name="select_dialog_singlechoice_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\select_dialog_singlechoice_material.xml" qualifiers="" type="layout"/><file name="support_simple_spinner_dropdown_item" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout\support_simple_spinner_dropdown_item.xml" qualifiers="" type="layout"/><file name="abc_alert_dialog_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout-v17\abc_alert_dialog_material.xml" qualifiers="v17" type="layout"/><file name="abc_dialog_title_material" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout-v17\abc_dialog_title_material.xml" qualifiers="v17" type="layout"/><file name="abc_search_view" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout-v17\abc_search_view.xml" qualifiers="v17" type="layout"/><file name="abc_screen_toolbar" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\layout-v21\abc_screen_toolbar.xml" qualifiers="v21" type="layout"/><file name="domino" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\menu\domino.xml" qualifiers="" type="menu"/><file name="menu_debug" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\menu\menu_debug.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values\attrs.xml" qualifiers=""><attr format="reference" name="actionBarDivider"/><attr format="reference" name="actionBarItemBackground"/><attr format="reference" name="actionBarPopupTheme"/><attr format="dimension" name="actionBarSize">
        <enum name="wrap_content" value="0"/>
    </attr><attr format="reference" name="actionBarSplitStyle"/><attr format="reference" name="actionBarStyle"/><attr format="reference" name="actionBarTabBarStyle"/><attr format="reference" name="actionBarTabStyle"/><attr format="reference" name="actionBarTabTextStyle"/><attr format="reference" name="actionBarTheme"/><attr format="reference" name="actionBarWidgetTheme"/><attr format="reference" name="actionButtonStyle"/><attr format="reference" name="actionDropDownStyle"/><attr format="reference" name="actionLayout"/><attr format="reference" name="actionMenuTextAppearance"/><attr format="reference|color" name="actionMenuTextColor"/><attr format="reference" name="actionModeBackground"/><attr format="reference" name="actionModeCloseButtonStyle"/><attr format="reference" name="actionModeCloseDrawable"/><attr format="reference" name="actionModeCopyDrawable"/><attr format="reference" name="actionModeCutDrawable"/><attr format="reference" name="actionModeFindDrawable"/><attr format="reference" name="actionModePasteDrawable"/><attr format="reference" name="actionModePopupWindowStyle"/><attr format="reference" name="actionModeSelectAllDrawable"/><attr format="reference" name="actionModeShareDrawable"/><attr format="reference" name="actionModeSplitBackground"/><attr format="reference" name="actionModeStyle"/><attr format="reference" name="actionModeWebSearchDrawable"/><attr format="reference" name="actionOverflowButtonStyle"/><attr format="reference" name="actionOverflowMenuStyle"/><attr format="string" name="actionProviderClass"/><attr format="string" name="actionViewClass"/><attr format="reference" name="activityChooserViewStyle"/><attr format="reference" name="alertDialogButtonGroupStyle"/><attr format="boolean" name="alertDialogCenterButtons"/><attr format="reference" name="alertDialogStyle"/><attr format="reference" name="alertDialogTheme"/><attr format="reference" name="autoCompleteTextViewStyle"/><attr format="reference" name="background"/><attr format="reference|color" name="backgroundSplit"/><attr format="reference|color" name="backgroundStacked"/><attr format="color" name="backgroundTint"/><attr name="backgroundTintMode">
        <enum name="multiply" value="14"/>
        <enum name="screen" value="15"/>
        <enum name="src_atop" value="9"/>
        <enum name="src_in" value="5"/>
        <enum name="src_over" value="3"/>
    </attr><attr format="dimension" name="barSize"/><attr format="reference" name="buttonBarButtonStyle"/><attr format="reference" name="buttonBarNegativeButtonStyle"/><attr format="reference" name="buttonBarNeutralButtonStyle"/><attr format="reference" name="buttonBarPositiveButtonStyle"/><attr format="reference" name="buttonBarStyle"/><attr format="reference" name="buttonPanelSideLayout"/><attr format="reference" name="buttonStyle"/><attr format="reference" name="buttonStyleSmall"/><attr format="reference" name="checkboxStyle"/><attr format="reference" name="checkedTextViewStyle"/><attr format="reference" name="closeIcon"/><attr format="reference" name="closeItemLayout"/><attr format="string" name="collapseContentDescription"/><attr format="reference" name="collapseIcon"/><attr format="color" name="color"/><attr format="color" name="colorAccent"/><attr format="color" name="colorButtonNormal"/><attr format="color" name="colorControlActivated"/><attr format="color" name="colorControlHighlight"/><attr format="color" name="colorControlNormal"/><attr format="color" name="colorPrimary"/><attr format="color" name="colorPrimaryDark"/><attr format="color" name="colorSwitchThumbNormal"/><attr format="reference" name="commitIcon"/><attr format="dimension" name="contentInsetEnd"/><attr format="dimension" name="contentInsetLeft"/><attr format="dimension" name="contentInsetRight"/><attr format="dimension" name="contentInsetStart"/><attr format="reference" name="customNavigationLayout"/><attr format="dimension" name="dialogPreferredPadding"/><attr format="reference" name="dialogTheme"/><attr format="boolean" name="disableChildrenWhenDisabled"/><attr name="displayOptions">
        <flag name="disableHome" value="0x00000020"/>
        <flag name="homeAsUp" value="0x00000004"/>
        <flag name="none" value="0x00000000"/>
        <flag name="showCustom" value="0x00000010"/>
        <flag name="showHome" value="0x00000002"/>
        <flag name="showTitle" value="0x00000008"/>
        <flag name="useLogo" value="0x00000001"/>
    </attr><attr format="reference" name="divider"/><attr format="reference" name="dividerHorizontal"/><attr format="dimension" name="dividerPadding"/><attr format="reference" name="dividerVertical"/><attr format="dimension" name="drawableSize"/><attr format="reference" name="drawerArrowStyle"/><attr format="reference" name="dropDownListViewStyle"/><attr format="dimension" name="dropdownListPreferredItemHeight"/><attr format="reference" name="editTextBackground"/><attr format="reference|color" name="editTextColor"/><attr format="reference" name="editTextStyle"/><attr format="dimension" name="elevation"/><attr format="reference" name="expandActivityOverflowButtonDrawable"/><attr format="dimension" name="gapBetweenBars"/><attr format="reference" name="goIcon"/><attr format="dimension" name="height"/><attr format="boolean" name="hideOnContentScroll"/><attr format="reference" name="homeAsUpIndicator"/><attr format="reference" name="homeLayout"/><attr format="reference" name="icon"/><attr format="boolean" name="iconifiedByDefault"/><attr format="reference" name="indeterminateProgressStyle"/><attr format="string" name="initialActivityCount"/><attr format="boolean" name="isLightTheme"/><attr format="dimension" name="itemPadding"/><attr format="reference" name="layout"/><attr format="reference" name="listChoiceBackgroundIndicator"/><attr format="reference" name="listDividerAlertDialog"/><attr format="reference" name="listItemLayout"/><attr format="reference" name="listLayout"/><attr format="reference" name="listPopupWindowStyle"/><attr format="dimension" name="listPreferredItemHeight"/><attr format="dimension" name="listPreferredItemHeightLarge"/><attr format="dimension" name="listPreferredItemHeightSmall"/><attr format="dimension" name="listPreferredItemPaddingLeft"/><attr format="dimension" name="listPreferredItemPaddingRight"/><attr format="reference" name="logo"/><attr format="dimension" name="maxButtonHeight"/><attr format="boolean" name="measureWithLargestChild"/><attr format="dimension" name="middleBarArrowSize"/><attr format="reference" name="multiChoiceItemLayout"/><attr format="string" name="navigationContentDescription"/><attr format="reference" name="navigationIcon"/><attr name="navigationMode">
        <enum name="listMode" value="1"/>
        <enum name="normal" value="0"/>
        <enum name="tabMode" value="2"/>
    </attr><attr format="boolean" name="overlapAnchor"/><attr format="dimension" name="paddingEnd"/><attr format="dimension" name="paddingStart"/><attr format="reference" name="panelBackground"/><attr format="reference" name="panelMenuListTheme"/><attr format="dimension" name="panelMenuListWidth"/><attr format="reference" name="popupMenuStyle"/><attr format="reference" name="popupPromptView"/><attr format="reference" name="popupTheme"/><attr format="reference" name="popupWindowStyle"/><attr format="boolean" name="preserveIconSpacing"/><attr format="dimension" name="progressBarPadding"/><attr format="reference" name="progressBarStyle"/><attr format="reference" name="prompt"/><attr format="reference" name="queryBackground"/><attr format="string" name="queryHint"/><attr format="reference" name="radioButtonStyle"/><attr format="reference" name="ratingBarStyle"/><attr format="reference" name="searchHintIcon"/><attr format="reference" name="searchIcon"/><attr format="reference" name="searchViewStyle"/><attr format="reference" name="selectableItemBackground"/><attr format="reference" name="selectableItemBackgroundBorderless"/><attr name="showAsAction">
        <flag name="always" value="0x00000002"/>
        <flag name="collapseActionView" value="0x00000008"/>
        <flag name="ifRoom" value="0x00000001"/>
        <flag name="never" value="0x00000000"/>
        <flag name="withText" value="0x00000004"/>
    </attr><attr name="showDividers">
        <flag name="beginning" value="0x00000001"/>
        <flag name="end" value="0x00000004"/>
        <flag name="middle" value="0x00000002"/>
        <flag name="none" value="0x00000000"/>
    </attr><attr format="boolean" name="showText"/><attr format="reference" name="singleChoiceItemLayout"/><attr format="boolean" name="spinBars"/><attr format="reference" name="spinnerDropDownItemStyle"/><attr name="spinnerMode">
        <enum name="dialog" value="0"/>
        <enum name="dropdown" value="1"/>
    </attr><attr format="reference" name="spinnerStyle"/><attr format="boolean" name="splitTrack"/><attr format="boolean" name="state_above_anchor"/><attr format="reference" name="submitBackground"/><attr format="string" name="subtitle"/><attr format="reference" name="subtitleTextAppearance"/><attr format="reference" name="subtitleTextStyle"/><attr format="reference" name="suggestionRowLayout"/><attr format="dimension" name="switchMinWidth"/><attr format="dimension" name="switchPadding"/><attr format="reference" name="switchStyle"/><attr format="reference" name="switchTextAppearance"/><attr format="reference|boolean" name="textAllCaps"/><attr format="reference" name="textAppearanceLargePopupMenu"/><attr format="reference" name="textAppearanceListItem"/><attr format="reference" name="textAppearanceListItemSmall"/><attr format="reference" name="textAppearanceSearchResultSubtitle"/><attr format="reference" name="textAppearanceSearchResultTitle"/><attr format="reference" name="textAppearanceSmallPopupMenu"/><attr format="reference|color" name="textColorAlertDialogListItem"/><attr format="reference|color" name="textColorSearchUrl"/><attr format="reference" name="theme"/><attr format="dimension" name="thickness"/><attr format="dimension" name="thumbTextPadding"/><attr format="string" name="title"/><attr format="dimension" name="titleMarginBottom"/><attr format="dimension" name="titleMarginEnd"/><attr format="dimension" name="titleMarginStart"/><attr format="dimension" name="titleMarginTop"/><attr format="dimension" name="titleMargins"/><attr format="reference" name="titleTextAppearance"/><attr format="reference" name="titleTextStyle"/><attr format="reference" name="toolbarNavigationButtonStyle"/><attr format="reference" name="toolbarStyle"/><attr format="dimension" name="topBottomBarArrowSize"/><attr format="reference" name="track"/><attr format="reference" name="voiceIcon"/><attr format="boolean" name="windowActionBar"/><attr format="boolean" name="windowActionBarOverlay"/><attr format="boolean" name="windowActionModeOverlay"/><attr format="dimension|fraction" name="windowFixedHeightMajor"/><attr format="dimension|fraction" name="windowFixedHeightMinor"/><attr format="dimension|fraction" name="windowFixedWidthMajor"/><attr format="dimension|fraction" name="windowFixedWidthMinor"/><attr format="dimension|fraction" name="windowMinWidthMajor"/><attr format="dimension|fraction" name="windowMinWidthMinor"/><attr format="boolean" name="windowNoTitle"/></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values\bools.xml" qualifiers=""><bool name="abc_action_bar_embed_tabs">true</bool><bool name="abc_action_bar_embed_tabs_pre_jb">false</bool><bool name="abc_action_bar_expanded_action_views_exclusive">true</bool><bool name="abc_config_actionMenuItemAllCaps">true</bool><bool name="abc_config_allowActionMenuItemTextWithIcon">false</bool><bool name="abc_config_closeDialogWhenTouchOutside">true</bool><bool name="abc_config_showMenuShortcutsWhenKeyboardPresent">false</bool></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values\colors.xml" qualifiers=""><color name="abc_input_method_navigation_guard">@android:color/black</color><color name="abc_search_url_text_normal">#ff7fa87f</color><color name="abc_search_url_text_pressed">@android:color/black</color><color name="abc_search_url_text_selected">@android:color/black</color><color name="accent_material_dark">@color/material_deep_teal_200</color><color name="accent_material_light">@color/material_deep_teal_500</color><color name="background_floating_material_dark">#ff424242</color><color name="background_floating_material_light">#ffeeeeee</color><color name="background_material_dark">#ff303030</color><color name="background_material_light">#ffeeeeee</color><color name="bright_foreground_disabled_material_dark">#80ffffff</color><color name="bright_foreground_disabled_material_light">#80000000</color><color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color><color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color><color name="bright_foreground_material_dark">@android:color/white</color><color name="bright_foreground_material_light">@android:color/black</color><color name="button_material_dark">#ff5a595b</color><color name="button_material_light">#ffd6d7d7</color><color name="dim_foreground_disabled_material_dark">#80bebebe</color><color name="dim_foreground_disabled_material_light">#80323232</color><color name="dim_foreground_material_dark">#ffbebebe</color><color name="dim_foreground_material_light">#ff323232</color><color name="highlighted_text_material_dark">#6680cbc4</color><color name="highlighted_text_material_light">#66009688</color><color name="hint_foreground_material_dark">@color/bright_foreground_disabled_material_dark</color><color name="hint_foreground_material_light">@color/bright_foreground_disabled_material_light</color><color name="link_text_material_dark">@color/material_deep_teal_200</color><color name="link_text_material_light">@color/material_deep_teal_500</color><color name="material_blue_grey_800">#ff37474f</color><color name="material_blue_grey_900">#ff263238</color><color name="material_blue_grey_950">#ff21272b</color><color name="material_deep_teal_200">#ff80cbc4</color><color name="material_deep_teal_500">#ff009688</color><color name="primary_dark_material_dark">#ff000000</color><color name="primary_dark_material_light">#ff757575</color><color name="primary_material_dark">#ff212121</color><color name="primary_material_light">#ffefefef</color><color name="primary_text_default_material_dark">#ffffffff</color><color name="primary_text_default_material_light">#de000000</color><color name="primary_text_disabled_material_dark">#4dffffff</color><color name="primary_text_disabled_material_light">#39000000</color><color name="ripple_material_dark">#4dffffff</color><color name="ripple_material_light">#1f000000</color><color name="secondary_text_default_material_dark">#b3ffffff</color><color name="secondary_text_default_material_light">#8a000000</color><color name="secondary_text_disabled_material_dark">#36ffffff</color><color name="secondary_text_disabled_material_light">#24000000</color><color name="switch_thumb_disabled_material_dark">#ff616161</color><color name="switch_thumb_disabled_material_light">#ffbdbdbd</color><color name="switch_thumb_normal_material_dark">#ffbdbdbd</color><color name="switch_thumb_normal_material_light">#fff1f1f1</color><color name="domino_primary">#ff2e7d32</color><color name="domino_primary_dark">#ff1b5e20</color><color name="domino_accent">#ffff6f00</color><color name="domino_background">#fff5f5f5</color><color name="domino_surface">#ffffffff</color><color name="domino_text_primary">#ff212121</color><color name="domino_text_secondary">#ff757575</color><color name="domino_button_positive">#ff4caf50</color><color name="domino_button_negative">#fff44336</color><color name="domino_button_neutral">#ff2196f3</color><color name="domino_game_board">#ff8bc34a</color><color name="domino_piece_white">#fffafafa</color><color name="domino_piece_black">#ff424242</color><color name="domino_score_background">#ffe8f5e8</color><color name="domino_player_highlight">#ffffe0b2</color><color name="domino_connection_success">#ff66bb6a</color><color name="domino_connection_error">#ffef5350</color><color name="domino_waiting">#ffffb74d</color></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="abc_action_bar_content_inset_material">16.0dip</dimen><dimen name="abc_action_bar_default_height_material">56.0dip</dimen><dimen name="abc_action_bar_default_padding_material">4.0dip</dimen><dimen name="abc_action_bar_icon_vertical_padding_material">16.0dip</dimen><dimen name="abc_action_bar_navigation_padding_start_material">0.0dip</dimen><dimen name="abc_action_bar_overflow_padding_end_material">10.0dip</dimen><dimen name="abc_action_bar_overflow_padding_start_material">6.0dip</dimen><dimen name="abc_action_bar_progress_bar_size">40.0dip</dimen><dimen name="abc_action_bar_stacked_max_height">48.0dip</dimen><dimen name="abc_action_bar_stacked_tab_max_width">180.0dip</dimen><dimen name="abc_action_bar_subtitle_bottom_margin_material">5.0dip</dimen><dimen name="abc_action_bar_subtitle_top_margin_material">-3.0dip</dimen><dimen name="abc_action_button_min_height_material">48.0dip</dimen><dimen name="abc_action_button_min_width_material">48.0dip</dimen><dimen name="abc_action_button_min_width_overflow_material">36.0dip</dimen><dimen name="abc_alert_dialog_button_bar_height">48.0dip</dimen><item name="abc_button_inset_horizontal_material" type="dimen">@dimen/abc_control_inset_material</item><dimen name="abc_button_inset_vertical_material">6.0dip</dimen><dimen name="abc_button_padding_horizontal_material">8.0dip</dimen><item name="abc_button_padding_vertical_material" type="dimen">@dimen/abc_control_padding_material</item><dimen name="abc_config_prefDialogWidth">320.0dip</dimen><dimen name="abc_control_corner_material">2.0dip</dimen><dimen name="abc_control_inset_material">4.0dip</dimen><dimen name="abc_control_padding_material">4.0dip</dimen><dimen name="abc_dialog_list_padding_vertical_material">8.0dip</dimen><item name="abc_dialog_min_width_major" type="dimen">65.0%</item><item name="abc_dialog_min_width_minor" type="dimen">95.00001%</item><dimen name="abc_dialog_padding_material">24.0dip</dimen><dimen name="abc_dialog_padding_top_material">18.0dip</dimen><item name="abc_disabled_alpha_material_dark" type="dimen">0.3</item><item name="abc_disabled_alpha_material_light" type="dimen">0.26</item><dimen name="abc_dropdownitem_icon_width">32.0dip</dimen><dimen name="abc_dropdownitem_text_padding_left">8.0dip</dimen><dimen name="abc_dropdownitem_text_padding_right">8.0dip</dimen><dimen name="abc_edit_text_inset_bottom_material">7.0dip</dimen><dimen name="abc_edit_text_inset_horizontal_material">4.0dip</dimen><dimen name="abc_edit_text_inset_top_material">10.0dip</dimen><dimen name="abc_floating_window_z">16.0dip</dimen><item name="abc_list_item_padding_horizontal_material" type="dimen">@dimen/abc_action_bar_content_inset_material</item><dimen name="abc_panel_menu_list_width">296.0dip</dimen><dimen name="abc_search_view_preferred_width">320.0dip</dimen><dimen name="abc_search_view_text_min_width">160.0dip</dimen><dimen name="abc_switch_padding">3.0dip</dimen><dimen name="abc_text_size_body_1_material">14.0sp</dimen><dimen name="abc_text_size_body_2_material">14.0sp</dimen><dimen name="abc_text_size_button_material">14.0sp</dimen><dimen name="abc_text_size_caption_material">12.0sp</dimen><dimen name="abc_text_size_display_1_material">34.0sp</dimen><dimen name="abc_text_size_display_2_material">45.0sp</dimen><dimen name="abc_text_size_display_3_material">56.0sp</dimen><dimen name="abc_text_size_display_4_material">112.0sp</dimen><dimen name="abc_text_size_headline_material">24.0sp</dimen><dimen name="abc_text_size_large_material">22.0sp</dimen><dimen name="abc_text_size_medium_material">18.0sp</dimen><dimen name="abc_text_size_menu_material">16.0sp</dimen><dimen name="abc_text_size_small_material">14.0sp</dimen><dimen name="abc_text_size_subhead_material">16.0sp</dimen><dimen name="abc_text_size_subtitle_material_toolbar">16.0dip</dimen><dimen name="abc_text_size_title_material">20.0sp</dimen><dimen name="abc_text_size_title_material_toolbar">20.0dip</dimen><dimen name="activity_horizontal_margin">16.0dip</dimen><dimen name="activity_vertical_margin">16.0dip</dimen><item name="dialog_fixed_height_major" type="dimen">79.99999%</item><item name="dialog_fixed_height_minor" type="dimen">100.0%</item><dimen name="dialog_fixed_width_major">320.0dip</dimen><dimen name="dialog_fixed_width_minor">320.0dip</dimen><item name="disabled_alpha_material_dark" type="dimen">0.3</item><item name="disabled_alpha_material_light" type="dimen">0.26</item></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values\ids.xml" qualifiers=""><item name="action_bar" type="id"/><item name="action_bar_activity_content" type="id"/><item name="action_bar_container" type="id"/><item name="action_bar_root" type="id"/><item name="action_bar_spinner" type="id"/><item name="action_bar_subtitle" type="id"/><item name="action_bar_title" type="id"/><item name="action_context_bar" type="id"/><item name="action_menu_divider" type="id"/><item name="action_menu_presenter" type="id"/><item name="action_mode_bar" type="id"/><item name="action_mode_bar_stub" type="id"/><item name="action_mode_close_button" type="id"/><item name="activity_chooser_view_content" type="id"/><item name="alertTitle" type="id"/><item name="buttonPanel" type="id"/><item name="checkbox" type="id"/><item name="contentPanel" type="id"/><item name="custom" type="id"/><item name="customPanel" type="id"/><item name="decor_content_parent" type="id"/><item name="default_activity_button" type="id"/><item name="edit_query" type="id"/><item name="expand_activities_button" type="id"/><item name="expanded_menu" type="id"/><item name="game_info" type="id"/><item name="game_settings" type="id"/><item name="home" type="id"/><item name="icon" type="id"/><item name="image" type="id"/><item name="list_item" type="id"/><item name="parentPanel" type="id"/><item name="progress_circular" type="id"/><item name="progress_horizontal" type="id"/><item name="radio" type="id"/><item name="scrollView" type="id"/><item name="search_badge" type="id"/><item name="search_bar" type="id"/><item name="search_button" type="id"/><item name="search_close_btn" type="id"/><item name="search_edit_frame" type="id"/><item name="search_go_btn" type="id"/><item name="search_mag_icon" type="id"/><item name="search_plate" type="id"/><item name="search_src_text" type="id"/><item name="search_voice_btn" type="id"/><item name="select_dialog_listview" type="id"/><item name="shortcut" type="id"/><item name="split_action_bar" type="id"/><item name="submit_area" type="id"/><item name="textSpacerNoButtons" type="id"/><item name="title" type="id"/><item name="title_template" type="id"/><item name="topPanel" type="id"/><item name="up" type="id"/></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values\integers.xml" qualifiers=""><integer name="abc_config_activityDefaultDur">220</integer><integer name="abc_config_activityShortDur">150</integer><integer name="abc_max_action_buttons">2</integer></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values\public.xml" qualifiers=""><public id="0x7f010000" name="abc_fade_in" type="anim"/><public id="0x7f010001" name="abc_fade_out" type="anim"/><public id="0x7f010002" name="abc_grow_fade_in_from_bottom" type="anim"/><public id="0x7f010003" name="abc_popup_enter" type="anim"/><public id="0x7f010004" name="abc_popup_exit" type="anim"/><public id="0x7f010005" name="abc_shrink_fade_out_from_bottom" type="anim"/><public id="0x7f010006" name="abc_slide_in_bottom" type="anim"/><public id="0x7f010007" name="abc_slide_in_top" type="anim"/><public id="0x7f010008" name="abc_slide_out_bottom" type="anim"/><public id="0x7f010009" name="abc_slide_out_top" type="anim"/><public id="0x7f020000" name="actionBarDivider" type="attr"/><public id="0x7f020001" name="actionBarItemBackground" type="attr"/><public id="0x7f020002" name="actionBarPopupTheme" type="attr"/><public id="0x7f020003" name="actionBarSize" type="attr"/><public id="0x7f020004" name="actionBarSplitStyle" type="attr"/><public id="0x7f020005" name="actionBarStyle" type="attr"/><public id="0x7f020006" name="actionBarTabBarStyle" type="attr"/><public id="0x7f020007" name="actionBarTabStyle" type="attr"/><public id="0x7f020008" name="actionBarTabTextStyle" type="attr"/><public id="0x7f020009" name="actionBarTheme" type="attr"/><public id="0x7f02000a" name="actionBarWidgetTheme" type="attr"/><public id="0x7f02000b" name="actionButtonStyle" type="attr"/><public id="0x7f02000c" name="actionDropDownStyle" type="attr"/><public id="0x7f02000d" name="actionLayout" type="attr"/><public id="0x7f02000e" name="actionMenuTextAppearance" type="attr"/><public id="0x7f02000f" name="actionMenuTextColor" type="attr"/><public id="0x7f020010" name="actionModeBackground" type="attr"/><public id="0x7f020011" name="actionModeCloseButtonStyle" type="attr"/><public id="0x7f020012" name="actionModeCloseDrawable" type="attr"/><public id="0x7f020013" name="actionModeCopyDrawable" type="attr"/><public id="0x7f020014" name="actionModeCutDrawable" type="attr"/><public id="0x7f020015" name="actionModeFindDrawable" type="attr"/><public id="0x7f020016" name="actionModePasteDrawable" type="attr"/><public id="0x7f020017" name="actionModePopupWindowStyle" type="attr"/><public id="0x7f020018" name="actionModeSelectAllDrawable" type="attr"/><public id="0x7f020019" name="actionModeShareDrawable" type="attr"/><public id="0x7f02001a" name="actionModeSplitBackground" type="attr"/><public id="0x7f02001b" name="actionModeStyle" type="attr"/><public id="0x7f02001c" name="actionModeWebSearchDrawable" type="attr"/><public id="0x7f02001d" name="actionOverflowButtonStyle" type="attr"/><public id="0x7f02001e" name="actionOverflowMenuStyle" type="attr"/><public id="0x7f02001f" name="actionProviderClass" type="attr"/><public id="0x7f020020" name="actionViewClass" type="attr"/><public id="0x7f020021" name="activityChooserViewStyle" type="attr"/><public id="0x7f020022" name="alertDialogButtonGroupStyle" type="attr"/><public id="0x7f020023" name="alertDialogCenterButtons" type="attr"/><public id="0x7f020024" name="alertDialogStyle" type="attr"/><public id="0x7f020025" name="alertDialogTheme" type="attr"/><public id="0x7f020026" name="autoCompleteTextViewStyle" type="attr"/><public id="0x7f020027" name="background" type="attr"/><public id="0x7f020028" name="backgroundSplit" type="attr"/><public id="0x7f020029" name="backgroundStacked" type="attr"/><public id="0x7f02002a" name="backgroundTint" type="attr"/><public id="0x7f02002b" name="backgroundTintMode" type="attr"/><public id="0x7f02002c" name="barSize" type="attr"/><public id="0x7f02002d" name="buttonBarButtonStyle" type="attr"/><public id="0x7f02002e" name="buttonBarNegativeButtonStyle" type="attr"/><public id="0x7f02002f" name="buttonBarNeutralButtonStyle" type="attr"/><public id="0x7f020030" name="buttonBarPositiveButtonStyle" type="attr"/><public id="0x7f020031" name="buttonBarStyle" type="attr"/><public id="0x7f020032" name="buttonPanelSideLayout" type="attr"/><public id="0x7f020033" name="buttonStyle" type="attr"/><public id="0x7f020034" name="buttonStyleSmall" type="attr"/><public id="0x7f020035" name="checkboxStyle" type="attr"/><public id="0x7f020036" name="checkedTextViewStyle" type="attr"/><public id="0x7f020037" name="closeIcon" type="attr"/><public id="0x7f020038" name="closeItemLayout" type="attr"/><public id="0x7f020039" name="collapseContentDescription" type="attr"/><public id="0x7f02003a" name="collapseIcon" type="attr"/><public id="0x7f02003b" name="color" type="attr"/><public id="0x7f02003c" name="colorAccent" type="attr"/><public id="0x7f02003d" name="colorButtonNormal" type="attr"/><public id="0x7f02003e" name="colorControlActivated" type="attr"/><public id="0x7f02003f" name="colorControlHighlight" type="attr"/><public id="0x7f020040" name="colorControlNormal" type="attr"/><public id="0x7f020041" name="colorPrimary" type="attr"/><public id="0x7f020042" name="colorPrimaryDark" type="attr"/><public id="0x7f020043" name="colorSwitchThumbNormal" type="attr"/><public id="0x7f020044" name="commitIcon" type="attr"/><public id="0x7f020045" name="contentInsetEnd" type="attr"/><public id="0x7f020046" name="contentInsetLeft" type="attr"/><public id="0x7f020047" name="contentInsetRight" type="attr"/><public id="0x7f020048" name="contentInsetStart" type="attr"/><public id="0x7f020049" name="customNavigationLayout" type="attr"/><public id="0x7f02004a" name="dialogPreferredPadding" type="attr"/><public id="0x7f02004b" name="dialogTheme" type="attr"/><public id="0x7f02004c" name="disableChildrenWhenDisabled" type="attr"/><public id="0x7f02004d" name="displayOptions" type="attr"/><public id="0x7f02004e" name="divider" type="attr"/><public id="0x7f02004f" name="dividerHorizontal" type="attr"/><public id="0x7f020050" name="dividerPadding" type="attr"/><public id="0x7f020051" name="dividerVertical" type="attr"/><public id="0x7f020052" name="drawableSize" type="attr"/><public id="0x7f020053" name="drawerArrowStyle" type="attr"/><public id="0x7f020054" name="dropDownListViewStyle" type="attr"/><public id="0x7f020055" name="dropdownListPreferredItemHeight" type="attr"/><public id="0x7f020056" name="editTextBackground" type="attr"/><public id="0x7f020057" name="editTextColor" type="attr"/><public id="0x7f020058" name="editTextStyle" type="attr"/><public id="0x7f020059" name="elevation" type="attr"/><public id="0x7f02005a" name="expandActivityOverflowButtonDrawable" type="attr"/><public id="0x7f02005b" name="gapBetweenBars" type="attr"/><public id="0x7f02005c" name="goIcon" type="attr"/><public id="0x7f02005d" name="height" type="attr"/><public id="0x7f02005e" name="hideOnContentScroll" type="attr"/><public id="0x7f02005f" name="homeAsUpIndicator" type="attr"/><public id="0x7f020060" name="homeLayout" type="attr"/><public id="0x7f020061" name="icon" type="attr"/><public id="0x7f020062" name="iconifiedByDefault" type="attr"/><public id="0x7f020063" name="indeterminateProgressStyle" type="attr"/><public id="0x7f020064" name="initialActivityCount" type="attr"/><public id="0x7f020065" name="isLightTheme" type="attr"/><public id="0x7f020066" name="itemPadding" type="attr"/><public id="0x7f020067" name="layout" type="attr"/><public id="0x7f020068" name="listChoiceBackgroundIndicator" type="attr"/><public id="0x7f020069" name="listDividerAlertDialog" type="attr"/><public id="0x7f02006a" name="listItemLayout" type="attr"/><public id="0x7f02006b" name="listLayout" type="attr"/><public id="0x7f02006c" name="listPopupWindowStyle" type="attr"/><public id="0x7f02006d" name="listPreferredItemHeight" type="attr"/><public id="0x7f02006e" name="listPreferredItemHeightLarge" type="attr"/><public id="0x7f02006f" name="listPreferredItemHeightSmall" type="attr"/><public id="0x7f020070" name="listPreferredItemPaddingLeft" type="attr"/><public id="0x7f020071" name="listPreferredItemPaddingRight" type="attr"/><public id="0x7f020072" name="logo" type="attr"/><public id="0x7f020073" name="maxButtonHeight" type="attr"/><public id="0x7f020074" name="measureWithLargestChild" type="attr"/><public id="0x7f020075" name="middleBarArrowSize" type="attr"/><public id="0x7f020076" name="multiChoiceItemLayout" type="attr"/><public id="0x7f020077" name="navigationContentDescription" type="attr"/><public id="0x7f020078" name="navigationIcon" type="attr"/><public id="0x7f020079" name="navigationMode" type="attr"/><public id="0x7f02007a" name="overlapAnchor" type="attr"/><public id="0x7f02007b" name="paddingEnd" type="attr"/><public id="0x7f02007c" name="paddingStart" type="attr"/><public id="0x7f02007d" name="panelBackground" type="attr"/><public id="0x7f02007e" name="panelMenuListTheme" type="attr"/><public id="0x7f02007f" name="panelMenuListWidth" type="attr"/><public id="0x7f020080" name="popupMenuStyle" type="attr"/><public id="0x7f020081" name="popupPromptView" type="attr"/><public id="0x7f020082" name="popupTheme" type="attr"/><public id="0x7f020083" name="popupWindowStyle" type="attr"/><public id="0x7f020084" name="preserveIconSpacing" type="attr"/><public id="0x7f020085" name="progressBarPadding" type="attr"/><public id="0x7f020086" name="progressBarStyle" type="attr"/><public id="0x7f020087" name="prompt" type="attr"/><public id="0x7f020088" name="queryBackground" type="attr"/><public id="0x7f020089" name="queryHint" type="attr"/><public id="0x7f02008a" name="radioButtonStyle" type="attr"/><public id="0x7f02008b" name="ratingBarStyle" type="attr"/><public id="0x7f02008c" name="searchHintIcon" type="attr"/><public id="0x7f02008d" name="searchIcon" type="attr"/><public id="0x7f02008e" name="searchViewStyle" type="attr"/><public id="0x7f02008f" name="selectableItemBackground" type="attr"/><public id="0x7f020090" name="selectableItemBackgroundBorderless" type="attr"/><public id="0x7f020091" name="showAsAction" type="attr"/><public id="0x7f020092" name="showDividers" type="attr"/><public id="0x7f020093" name="showText" type="attr"/><public id="0x7f020094" name="singleChoiceItemLayout" type="attr"/><public id="0x7f020095" name="spinBars" type="attr"/><public id="0x7f020096" name="spinnerDropDownItemStyle" type="attr"/><public id="0x7f020097" name="spinnerMode" type="attr"/><public id="0x7f020098" name="spinnerStyle" type="attr"/><public id="0x7f020099" name="splitTrack" type="attr"/><public id="0x7f02009a" name="state_above_anchor" type="attr"/><public id="0x7f02009b" name="submitBackground" type="attr"/><public id="0x7f02009c" name="subtitle" type="attr"/><public id="0x7f02009d" name="subtitleTextAppearance" type="attr"/><public id="0x7f02009e" name="subtitleTextStyle" type="attr"/><public id="0x7f02009f" name="suggestionRowLayout" type="attr"/><public id="0x7f0200a0" name="switchMinWidth" type="attr"/><public id="0x7f0200a1" name="switchPadding" type="attr"/><public id="0x7f0200a2" name="switchStyle" type="attr"/><public id="0x7f0200a3" name="switchTextAppearance" type="attr"/><public id="0x7f0200a4" name="textAllCaps" type="attr"/><public id="0x7f0200a5" name="textAppearanceLargePopupMenu" type="attr"/><public id="0x7f0200a6" name="textAppearanceListItem" type="attr"/><public id="0x7f0200a7" name="textAppearanceListItemSmall" type="attr"/><public id="0x7f0200a8" name="textAppearanceSearchResultSubtitle" type="attr"/><public id="0x7f0200a9" name="textAppearanceSearchResultTitle" type="attr"/><public id="0x7f0200aa" name="textAppearanceSmallPopupMenu" type="attr"/><public id="0x7f0200ab" name="textColorAlertDialogListItem" type="attr"/><public id="0x7f0200ac" name="textColorSearchUrl" type="attr"/><public id="0x7f0200ad" name="theme" type="attr"/><public id="0x7f0200ae" name="thickness" type="attr"/><public id="0x7f0200af" name="thumbTextPadding" type="attr"/><public id="0x7f0200b0" name="title" type="attr"/><public id="0x7f0200b1" name="titleMarginBottom" type="attr"/><public id="0x7f0200b2" name="titleMarginEnd" type="attr"/><public id="0x7f0200b3" name="titleMarginStart" type="attr"/><public id="0x7f0200b4" name="titleMarginTop" type="attr"/><public id="0x7f0200b5" name="titleMargins" type="attr"/><public id="0x7f0200b6" name="titleTextAppearance" type="attr"/><public id="0x7f0200b7" name="titleTextStyle" type="attr"/><public id="0x7f0200b8" name="toolbarNavigationButtonStyle" type="attr"/><public id="0x7f0200b9" name="toolbarStyle" type="attr"/><public id="0x7f0200ba" name="topBottomBarArrowSize" type="attr"/><public id="0x7f0200bb" name="track" type="attr"/><public id="0x7f0200bc" name="voiceIcon" type="attr"/><public id="0x7f0200bd" name="windowActionBar" type="attr"/><public id="0x7f0200be" name="windowActionBarOverlay" type="attr"/><public id="0x7f0200bf" name="windowActionModeOverlay" type="attr"/><public id="0x7f0200c0" name="windowFixedHeightMajor" type="attr"/><public id="0x7f0200c1" name="windowFixedHeightMinor" type="attr"/><public id="0x7f0200c2" name="windowFixedWidthMajor" type="attr"/><public id="0x7f0200c3" name="windowFixedWidthMinor" type="attr"/><public id="0x7f0200c4" name="windowMinWidthMajor" type="attr"/><public id="0x7f0200c5" name="windowMinWidthMinor" type="attr"/><public id="0x7f0200c6" name="windowNoTitle" type="attr"/><public id="0x7f030000" name="abc_action_bar_embed_tabs" type="bool"/><public id="0x7f030001" name="abc_action_bar_embed_tabs_pre_jb" type="bool"/><public id="0x7f030002" name="abc_action_bar_expanded_action_views_exclusive" type="bool"/><public id="0x7f030003" name="abc_config_actionMenuItemAllCaps" type="bool"/><public id="0x7f030004" name="abc_config_allowActionMenuItemTextWithIcon" type="bool"/><public id="0x7f030005" name="abc_config_closeDialogWhenTouchOutside" type="bool"/><public id="0x7f030006" name="abc_config_showMenuShortcutsWhenKeyboardPresent" type="bool"/><public id="0x7f040000" name="abc_background_cache_hint_selector_material_dark" type="color"/><public id="0x7f040001" name="abc_background_cache_hint_selector_material_light" type="color"/><public id="0x7f040002" name="abc_input_method_navigation_guard" type="color"/><public id="0x7f040003" name="abc_primary_text_disable_only_material_dark" type="color"/><public id="0x7f040004" name="abc_primary_text_disable_only_material_light" type="color"/><public id="0x7f040005" name="abc_primary_text_material_dark" type="color"/><public id="0x7f040006" name="abc_primary_text_material_light" type="color"/><public id="0x7f040007" name="abc_search_url_text" type="color"/><public id="0x7f040008" name="abc_search_url_text_normal" type="color"/><public id="0x7f040009" name="abc_search_url_text_pressed" type="color"/><public id="0x7f04000a" name="abc_search_url_text_selected" type="color"/><public id="0x7f04000b" name="abc_secondary_text_material_dark" type="color"/><public id="0x7f04000c" name="abc_secondary_text_material_light" type="color"/><public id="0x7f04000d" name="accent_material_dark" type="color"/><public id="0x7f04000e" name="accent_material_light" type="color"/><public id="0x7f04000f" name="background_floating_material_dark" type="color"/><public id="0x7f040010" name="background_floating_material_light" type="color"/><public id="0x7f040011" name="background_material_dark" type="color"/><public id="0x7f040012" name="background_material_light" type="color"/><public id="0x7f040013" name="bright_foreground_disabled_material_dark" type="color"/><public id="0x7f040014" name="bright_foreground_disabled_material_light" type="color"/><public id="0x7f040015" name="bright_foreground_inverse_material_dark" type="color"/><public id="0x7f040016" name="bright_foreground_inverse_material_light" type="color"/><public id="0x7f040017" name="bright_foreground_material_dark" type="color"/><public id="0x7f040018" name="bright_foreground_material_light" type="color"/><public id="0x7f040019" name="button_material_dark" type="color"/><public id="0x7f04001a" name="button_material_light" type="color"/><public id="0x7f04001b" name="dim_foreground_disabled_material_dark" type="color"/><public id="0x7f04001c" name="dim_foreground_disabled_material_light" type="color"/><public id="0x7f04001d" name="dim_foreground_material_dark" type="color"/><public id="0x7f04001e" name="dim_foreground_material_light" type="color"/><public id="0x7f04001f" name="highlighted_text_material_dark" type="color"/><public id="0x7f040020" name="highlighted_text_material_light" type="color"/><public id="0x7f040021" name="hint_foreground_material_dark" type="color"/><public id="0x7f040022" name="hint_foreground_material_light" type="color"/><public id="0x7f040023" name="link_text_material_dark" type="color"/><public id="0x7f040024" name="link_text_material_light" type="color"/><public id="0x7f040025" name="material_blue_grey_800" type="color"/><public id="0x7f040026" name="material_blue_grey_900" type="color"/><public id="0x7f040027" name="material_blue_grey_950" type="color"/><public id="0x7f040028" name="material_deep_teal_200" type="color"/><public id="0x7f040029" name="material_deep_teal_500" type="color"/><public id="0x7f04002a" name="primary_dark_material_dark" type="color"/><public id="0x7f04002b" name="primary_dark_material_light" type="color"/><public id="0x7f04002c" name="primary_material_dark" type="color"/><public id="0x7f04002d" name="primary_material_light" type="color"/><public id="0x7f04002e" name="primary_text_default_material_dark" type="color"/><public id="0x7f04002f" name="primary_text_default_material_light" type="color"/><public id="0x7f040030" name="primary_text_disabled_material_dark" type="color"/><public id="0x7f040031" name="primary_text_disabled_material_light" type="color"/><public id="0x7f040032" name="ripple_material_dark" type="color"/><public id="0x7f040033" name="ripple_material_light" type="color"/><public id="0x7f040034" name="secondary_text_default_material_dark" type="color"/><public id="0x7f040035" name="secondary_text_default_material_light" type="color"/><public id="0x7f040036" name="secondary_text_disabled_material_dark" type="color"/><public id="0x7f040037" name="secondary_text_disabled_material_light" type="color"/><public id="0x7f040038" name="switch_thumb_disabled_material_dark" type="color"/><public id="0x7f040039" name="switch_thumb_disabled_material_light" type="color"/><public id="0x7f04003a" name="switch_thumb_material_dark" type="color"/><public id="0x7f04003b" name="switch_thumb_material_light" type="color"/><public id="0x7f04003c" name="switch_thumb_normal_material_dark" type="color"/><public id="0x7f04003d" name="switch_thumb_normal_material_light" type="color"/><public id="0x7f050000" name="abc_action_bar_content_inset_material" type="dimen"/><public id="0x7f050001" name="abc_action_bar_default_height_material" type="dimen"/><public id="0x7f050002" name="abc_action_bar_default_padding_material" type="dimen"/><public id="0x7f050003" name="abc_action_bar_icon_vertical_padding_material" type="dimen"/><public id="0x7f050004" name="abc_action_bar_navigation_padding_start_material" type="dimen"/><public id="0x7f050005" name="abc_action_bar_overflow_padding_end_material" type="dimen"/><public id="0x7f050006" name="abc_action_bar_overflow_padding_start_material" type="dimen"/><public id="0x7f050007" name="abc_action_bar_progress_bar_size" type="dimen"/><public id="0x7f050008" name="abc_action_bar_stacked_max_height" type="dimen"/><public id="0x7f050009" name="abc_action_bar_stacked_tab_max_width" type="dimen"/><public id="0x7f05000a" name="abc_action_bar_subtitle_bottom_margin_material" type="dimen"/><public id="0x7f05000b" name="abc_action_bar_subtitle_top_margin_material" type="dimen"/><public id="0x7f05000c" name="abc_action_button_min_height_material" type="dimen"/><public id="0x7f05000d" name="abc_action_button_min_width_material" type="dimen"/><public id="0x7f05000e" name="abc_action_button_min_width_overflow_material" type="dimen"/><public id="0x7f05000f" name="abc_alert_dialog_button_bar_height" type="dimen"/><public id="0x7f050010" name="abc_button_inset_horizontal_material" type="dimen"/><public id="0x7f050011" name="abc_button_inset_vertical_material" type="dimen"/><public id="0x7f050012" name="abc_button_padding_horizontal_material" type="dimen"/><public id="0x7f050013" name="abc_button_padding_vertical_material" type="dimen"/><public id="0x7f050014" name="abc_config_prefDialogWidth" type="dimen"/><public id="0x7f050015" name="abc_control_corner_material" type="dimen"/><public id="0x7f050016" name="abc_control_inset_material" type="dimen"/><public id="0x7f050017" name="abc_control_padding_material" type="dimen"/><public id="0x7f050018" name="abc_dialog_list_padding_vertical_material" type="dimen"/><public id="0x7f050019" name="abc_dialog_min_width_major" type="dimen"/><public id="0x7f05001a" name="abc_dialog_min_width_minor" type="dimen"/><public id="0x7f05001b" name="abc_dialog_padding_material" type="dimen"/><public id="0x7f05001c" name="abc_dialog_padding_top_material" type="dimen"/><public id="0x7f05001d" name="abc_disabled_alpha_material_dark" type="dimen"/><public id="0x7f05001e" name="abc_disabled_alpha_material_light" type="dimen"/><public id="0x7f05001f" name="abc_dropdownitem_icon_width" type="dimen"/><public id="0x7f050020" name="abc_dropdownitem_text_padding_left" type="dimen"/><public id="0x7f050021" name="abc_dropdownitem_text_padding_right" type="dimen"/><public id="0x7f050022" name="abc_edit_text_inset_bottom_material" type="dimen"/><public id="0x7f050023" name="abc_edit_text_inset_horizontal_material" type="dimen"/><public id="0x7f050024" name="abc_edit_text_inset_top_material" type="dimen"/><public id="0x7f050025" name="abc_floating_window_z" type="dimen"/><public id="0x7f050026" name="abc_list_item_padding_horizontal_material" type="dimen"/><public id="0x7f050027" name="abc_panel_menu_list_width" type="dimen"/><public id="0x7f050028" name="abc_search_view_preferred_width" type="dimen"/><public id="0x7f050029" name="abc_search_view_text_min_width" type="dimen"/><public id="0x7f05002a" name="abc_switch_padding" type="dimen"/><public id="0x7f05002b" name="abc_text_size_body_1_material" type="dimen"/><public id="0x7f05002c" name="abc_text_size_body_2_material" type="dimen"/><public id="0x7f05002d" name="abc_text_size_button_material" type="dimen"/><public id="0x7f05002e" name="abc_text_size_caption_material" type="dimen"/><public id="0x7f05002f" name="abc_text_size_display_1_material" type="dimen"/><public id="0x7f050030" name="abc_text_size_display_2_material" type="dimen"/><public id="0x7f050031" name="abc_text_size_display_3_material" type="dimen"/><public id="0x7f050032" name="abc_text_size_display_4_material" type="dimen"/><public id="0x7f050033" name="abc_text_size_headline_material" type="dimen"/><public id="0x7f050034" name="abc_text_size_large_material" type="dimen"/><public id="0x7f050035" name="abc_text_size_medium_material" type="dimen"/><public id="0x7f050036" name="abc_text_size_menu_material" type="dimen"/><public id="0x7f050037" name="abc_text_size_small_material" type="dimen"/><public id="0x7f050038" name="abc_text_size_subhead_material" type="dimen"/><public id="0x7f050039" name="abc_text_size_subtitle_material_toolbar" type="dimen"/><public id="0x7f05003a" name="abc_text_size_title_material" type="dimen"/><public id="0x7f05003b" name="abc_text_size_title_material_toolbar" type="dimen"/><public id="0x7f05003c" name="activity_horizontal_margin" type="dimen"/><public id="0x7f05003d" name="activity_vertical_margin" type="dimen"/><public id="0x7f05003e" name="dialog_fixed_height_major" type="dimen"/><public id="0x7f05003f" name="dialog_fixed_height_minor" type="dimen"/><public id="0x7f050040" name="dialog_fixed_width_major" type="dimen"/><public id="0x7f050041" name="dialog_fixed_width_minor" type="dimen"/><public id="0x7f050042" name="disabled_alpha_material_dark" type="dimen"/><public id="0x7f050043" name="disabled_alpha_material_light" type="dimen"/><public id="0x7f060001" name="abc_btn_borderless_material" type="drawable"/><public id="0x7f060002" name="abc_btn_check_material" type="drawable"/><public id="0x7f060005" name="abc_btn_default_mtrl_shape" type="drawable"/><public id="0x7f060006" name="abc_btn_radio_material" type="drawable"/><public id="0x7f06000d" name="abc_cab_background_internal_bg" type="drawable"/><public id="0x7f06000e" name="abc_cab_background_top_material" type="drawable"/><public id="0x7f060010" name="abc_dialog_material_background_dark" type="drawable"/><public id="0x7f060011" name="abc_dialog_material_background_light" type="drawable"/><public id="0x7f060012" name="abc_edit_text_material" type="drawable"/><public id="0x7f06001f" name="abc_item_background_holo_dark" type="drawable"/><public id="0x7f060020" name="abc_item_background_holo_light" type="drawable"/><public id="0x7f060026" name="abc_list_selector_background_transition_holo_dark" type="drawable"/><public id="0x7f060027" name="abc_list_selector_background_transition_holo_light" type="drawable"/><public id="0x7f06002a" name="abc_list_selector_holo_dark" type="drawable"/><public id="0x7f06002b" name="abc_list_selector_holo_light" type="drawable"/><public id="0x7f06002e" name="abc_ratingbar_full_material" type="drawable"/><public id="0x7f060030" name="abc_spinner_textfield_background_material" type="drawable"/><public id="0x7f060031" name="abc_switch_thumb_material" type="drawable"/><public id="0x7f060033" name="abc_tab_indicator_material" type="drawable"/><public id="0x7f06003a" name="abc_textfield_search_material" type="drawable"/><public id="0x7f070000" name="action_bar" type="id"/><public id="0x7f070001" name="action_bar_activity_content" type="id"/><public id="0x7f070002" name="action_bar_container" type="id"/><public id="0x7f070003" name="action_bar_root" type="id"/><public id="0x7f070004" name="action_bar_spinner" type="id"/><public id="0x7f070005" name="action_bar_subtitle" type="id"/><public id="0x7f070006" name="action_bar_title" type="id"/><public id="0x7f070007" name="action_context_bar" type="id"/><public id="0x7f070008" name="action_menu_divider" type="id"/><public id="0x7f070009" name="action_menu_presenter" type="id"/><public id="0x7f07000a" name="action_mode_bar" type="id"/><public id="0x7f07000b" name="action_mode_bar_stub" type="id"/><public id="0x7f07000c" name="action_mode_close_button" type="id"/><public id="0x7f07000d" name="activity_chooser_view_content" type="id"/><public id="0x7f07000e" name="alertTitle" type="id"/><public id="0x7f07000f" name="always" type="id"/><public id="0x7f070010" name="beginning" type="id"/><public id="0x7f070011" name="buttonPanel" type="id"/><public id="0x7f070012" name="checkbox" type="id"/><public id="0x7f070013" name="collapseActionView" type="id"/><public id="0x7f070014" name="contentPanel" type="id"/><public id="0x7f070015" name="custom" type="id"/><public id="0x7f070016" name="customPanel" type="id"/><public id="0x7f070017" name="decor_content_parent" type="id"/><public id="0x7f070018" name="default_activity_button" type="id"/><public id="0x7f070019" name="dialog" type="id"/><public id="0x7f07001a" name="disableHome" type="id"/><public id="0x7f07001b" name="dropdown" type="id"/><public id="0x7f07001c" name="edit_query" type="id"/><public id="0x7f07001d" name="end" type="id"/><public id="0x7f07001e" name="expand_activities_button" type="id"/><public id="0x7f07001f" name="expanded_menu" type="id"/><public id="0x7f070020" name="game_info" type="id"/><public id="0x7f070021" name="game_settings" type="id"/><public id="0x7f070022" name="home" type="id"/><public id="0x7f070023" name="homeAsUp" type="id"/><public id="0x7f070024" name="icon" type="id"/><public id="0x7f070025" name="ifRoom" type="id"/><public id="0x7f070026" name="image" type="id"/><public id="0x7f070027" name="listMode" type="id"/><public id="0x7f070028" name="list_item" type="id"/><public id="0x7f070029" name="middle" type="id"/><public id="0x7f07002a" name="multiply" type="id"/><public id="0x7f07002b" name="never" type="id"/><public id="0x7f07002c" name="none" type="id"/><public id="0x7f07002d" name="normal" type="id"/><public id="0x7f07002e" name="parentPanel" type="id"/><public id="0x7f07002f" name="progress_circular" type="id"/><public id="0x7f070030" name="progress_horizontal" type="id"/><public id="0x7f070031" name="radio" type="id"/><public id="0x7f070032" name="screen" type="id"/><public id="0x7f070033" name="scrollView" type="id"/><public id="0x7f070034" name="search_badge" type="id"/><public id="0x7f070035" name="search_bar" type="id"/><public id="0x7f070036" name="search_button" type="id"/><public id="0x7f070037" name="search_close_btn" type="id"/><public id="0x7f070038" name="search_edit_frame" type="id"/><public id="0x7f070039" name="search_go_btn" type="id"/><public id="0x7f07003a" name="search_mag_icon" type="id"/><public id="0x7f07003b" name="search_plate" type="id"/><public id="0x7f07003c" name="search_src_text" type="id"/><public id="0x7f07003d" name="search_voice_btn" type="id"/><public id="0x7f07003e" name="select_dialog_listview" type="id"/><public id="0x7f07003f" name="shortcut" type="id"/><public id="0x7f070040" name="showCustom" type="id"/><public id="0x7f070041" name="showHome" type="id"/><public id="0x7f070042" name="showTitle" type="id"/><public id="0x7f070043" name="split_action_bar" type="id"/><public id="0x7f070044" name="src_atop" type="id"/><public id="0x7f070045" name="src_in" type="id"/><public id="0x7f070046" name="src_over" type="id"/><public id="0x7f070047" name="submit_area" type="id"/><public id="0x7f070048" name="tabMode" type="id"/><public id="0x7f070049" name="textSpacerNoButtons" type="id"/><public id="0x7f07004a" name="title" type="id"/><public id="0x7f07004b" name="title_template" type="id"/><public id="0x7f07004c" name="topPanel" type="id"/><public id="0x7f07004d" name="up" type="id"/><public id="0x7f07004e" name="useLogo" type="id"/><public id="0x7f07004f" name="withText" type="id"/><public id="0x7f070050" name="wrap_content" type="id"/><public id="0x7f080000" name="abc_config_activityDefaultDur" type="integer"/><public id="0x7f080001" name="abc_config_activityShortDur" type="integer"/><public id="0x7f080002" name="abc_max_action_buttons" type="integer"/><public id="0x7f090000" name="abc_action_bar_title_item" type="layout"/><public id="0x7f090001" name="abc_action_bar_up_container" type="layout"/><public id="0x7f090002" name="abc_action_bar_view_list_nav_layout" type="layout"/><public id="0x7f090003" name="abc_action_menu_item_layout" type="layout"/><public id="0x7f090004" name="abc_action_menu_layout" type="layout"/><public id="0x7f090005" name="abc_action_mode_bar" type="layout"/><public id="0x7f090006" name="abc_action_mode_close_item_material" type="layout"/><public id="0x7f090007" name="abc_activity_chooser_view" type="layout"/><public id="0x7f090008" name="abc_activity_chooser_view_list_item" type="layout"/><public id="0x7f090009" name="abc_alert_dialog_material" type="layout"/><public id="0x7f09000a" name="abc_dialog_title_material" type="layout"/><public id="0x7f09000b" name="abc_expanded_menu_layout" type="layout"/><public id="0x7f09000c" name="abc_list_menu_item_checkbox" type="layout"/><public id="0x7f09000d" name="abc_list_menu_item_icon" type="layout"/><public id="0x7f09000e" name="abc_list_menu_item_layout" type="layout"/><public id="0x7f09000f" name="abc_list_menu_item_radio" type="layout"/><public id="0x7f090010" name="abc_popup_menu_item_layout" type="layout"/><public id="0x7f090011" name="abc_screen_content_include" type="layout"/><public id="0x7f090012" name="abc_screen_simple" type="layout"/><public id="0x7f090013" name="abc_screen_simple_overlay_action_mode" type="layout"/><public id="0x7f090014" name="abc_screen_toolbar" type="layout"/><public id="0x7f090015" name="abc_search_dropdown_item_icons_2line" type="layout"/><public id="0x7f090016" name="abc_search_view" type="layout"/><public id="0x7f090017" name="abc_select_dialog_material" type="layout"/><public id="0x7f090018" name="abc_simple_dropdown_hint" type="layout"/><public id="0x7f090019" name="activity_debug" type="layout"/><public id="0x7f09001a" name="list_item" type="layout"/><public id="0x7f09001b" name="select_dialog_item_material" type="layout"/><public id="0x7f09001c" name="select_dialog_multichoice_material" type="layout"/><public id="0x7f09001d" name="select_dialog_singlechoice_material" type="layout"/><public id="0x7f09001e" name="support_simple_spinner_dropdown_item" type="layout"/><public id="0x7f0a0000" name="domino" type="menu"/><public id="0x7f0a0001" name="menu_debug" type="menu"/><public id="0x7f0b0000" name="ic_launcher" type="mipmap"/><public id="0x7f0c0000" name="abc_action_bar_home_description" type="string"/><public id="0x7f0c0001" name="abc_action_bar_home_description_format" type="string"/><public id="0x7f0c0002" name="abc_action_bar_home_subtitle_description_format" type="string"/><public id="0x7f0c0003" name="abc_action_bar_up_description" type="string"/><public id="0x7f0c0004" name="abc_action_menu_overflow_description" type="string"/><public id="0x7f0c0005" name="abc_action_mode_done" type="string"/><public id="0x7f0c0006" name="abc_activity_chooser_view_see_all" type="string"/><public id="0x7f0c0007" name="abc_activitychooserview_choose_application" type="string"/><public id="0x7f0c0008" name="abc_search_hint" type="string"/><public id="0x7f0c0009" name="abc_searchview_description_clear" type="string"/><public id="0x7f0c000a" name="abc_searchview_description_query" type="string"/><public id="0x7f0c000b" name="abc_searchview_description_search" type="string"/><public id="0x7f0c000c" name="abc_searchview_description_submit" type="string"/><public id="0x7f0c000d" name="abc_searchview_description_voice" type="string"/><public id="0x7f0c000e" name="abc_shareactionprovider_share_with" type="string"/><public id="0x7f0c000f" name="abc_shareactionprovider_share_with_application" type="string"/><public id="0x7f0c0010" name="abc_toolbar_collapse_description" type="string"/><public id="0x7f0c0011" name="about_label" type="string"/><public id="0x7f0c0012" name="action_settings" type="string"/><public id="0x7f0c0013" name="ai1" type="string"/><public id="0x7f0c0014" name="ai2" type="string"/><public id="0x7f0c0015" name="ai3" type="string"/><public id="0x7f0c0016" name="app_name" type="string"/><public id="0x7f0c0017" name="blue" type="string"/><public id="0x7f0c0018" name="button_new_game" type="string"/><public id="0x7f0c0019" name="cancel" type="string"/><public id="0x7f0c001a" name="clear_debug" type="string"/><public id="0x7f0c001b" name="close" type="string"/><public id="0x7f0c001c" name="connect" type="string"/><public id="0x7f0c001d" name="connection_error" type="string"/><public id="0x7f0c001e" name="continue_label" type="string"/><public id="0x7f0c001f" name="create_group" type="string"/><public id="0x7f0c0020" name="creator_team" type="string"/><public id="0x7f0c0021" name="cuban_domino" type="string"/><public id="0x7f0c0022" name="debug" type="string"/><public id="0x7f0c0023" name="debug_info" type="string"/><public id="0x7f0c0024" name="demo_mode" type="string"/><public id="0x7f0c0025" name="devices" type="string"/><public id="0x7f0c0026" name="different_server_version" type="string"/><public id="0x7f0c0027" name="do_you_want_to_start" type="string"/><public id="0x7f0c0028" name="domino_set" type="string"/><public id="0x7f0c0029" name="empty_player_name" type="string"/><public id="0x7f0c002a" name="error" type="string"/><public id="0x7f0c002b" name="error_bluetooth_not_supported" type="string"/><public id="0x7f0c002c" name="error_getting_game_data" type="string"/><public id="0x7f0c002d" name="exit_game_msg" type="string"/><public id="0x7f0c002e" name="exit_label" type="string"/><public id="0x7f0c002f" name="fast" type="string"/><public id="0x7f0c0030" name="game_creator" type="string"/><public id="0x7f0c0031" name="game_draw" type="string"/><public id="0x7f0c0032" name="game_info" type="string"/><public id="0x7f0c0033" name="game_over" type="string"/><public id="0x7f0c0034" name="game_setup" type="string"/><public id="0x7f0c0035" name="getting_game_data" type="string"/><public id="0x7f0c0036" name="hello" type="string"/><public id="0x7f0c0037" name="hello_world" type="string"/><public id="0x7f0c0038" name="i_passed" type="string"/><public id="0x7f0c0039" name="its_better_if_you_start" type="string"/><public id="0x7f0c003a" name="join_group" type="string"/><public id="0x7f0c003b" name="joining_game" type="string"/><public id="0x7f0c003c" name="let_me_start" type="string"/><public id="0x7f0c003d" name="main_title" type="string"/><public id="0x7f0c003e" name="max_score" type="string"/><public id="0x7f0c003f" name="me" type="string"/><public id="0x7f0c0040" name="must_select_device" type="string"/><public id="0x7f0c0041" name="network" type="string"/><public id="0x7f0c0042" name="new_game" type="string"/><public id="0x7f0c0043" name="new_game_label" type="string"/><public id="0x7f0c0044" name="next_round" type="string"/><public id="0x7f0c0045" name="no" type="string"/><public id="0x7f0c0046" name="no_devices" type="string"/><public id="0x7f0c0047" name="no_let_me_start" type="string"/><public id="0x7f0c0048" name="no_way_you_start" type="string"/><public id="0x7f0c0049" name="normal" type="string"/><public id="0x7f0c004a" name="ok" type="string"/><public id="0x7f0c004b" name="ok_i_will_start" type="string"/><public id="0x7f0c004c" name="ok_you_start" type="string"/><public id="0x7f0c004d" name="pale" type="string"/><public id="0x7f0c004e" name="pieces_28" type="string"/><public id="0x7f0c004f" name="pieces_55" type="string"/><public id="0x7f0c0050" name="player_passed" type="string"/><public id="0x7f0c0051" name="player_playing" type="string"/><public id="0x7f0c0052" name="player_thinking" type="string"/><public id="0x7f0c0053" name="players" type="string"/><public id="0x7f0c0054" name="ready" type="string"/><public id="0x7f0c0055" name="ready_to_start" type="string"/><public id="0x7f0c0056" name="retry" type="string"/><public id="0x7f0c0057" name="round_over" type="string"/><public id="0x7f0c0058" name="scanning" type="string"/><public id="0x7f0c0059" name="score" type="string"/><public id="0x7f0c005a" name="settings" type="string"/><public id="0x7f0c005b" name="slow" type="string"/><public id="0x7f0c005c" name="speed" type="string"/><public id="0x7f0c005d" name="start" type="string"/><public id="0x7f0c005e" name="start_game" type="string"/><public id="0x7f0c005f" name="team_play" type="string"/><public id="0x7f0c0060" name="theme" type="string"/><public id="0x7f0c0061" name="title_activity_connection" type="string"/><public id="0x7f0c0062" name="title_activity_debug" type="string"/><public id="0x7f0c0063" name="title_activity_domino" type="string"/><public id="0x7f0c0064" name="unable_to_join" type="string"/><public id="0x7f0c0065" name="version" type="string"/><public id="0x7f0c0066" name="waiting_for_new_round" type="string"/><public id="0x7f0c0067" name="waiting_for_players" type="string"/><public id="0x7f0c0068" name="winner_player" type="string"/><public id="0x7f0c0069" name="yes" type="string"/><public id="0x7f0c006a" name="your_name" type="string"/><public id="0x7f0c006b" name="your_name_title" type="string"/><public id="0x7f0c006c" name="your_partner_doesnt_wants_to_start" type="string"/><public id="0x7f0c006d" name="your_partner_wants_to_start" type="string"/><public id="0x7f0d0000" name="AlertDialog.AppCompat" type="style"/><public id="0x7f0d0001" name="AlertDialog.AppCompat.Light" type="style"/><public id="0x7f0d0002" name="Animation.AppCompat.Dialog" type="style"/><public id="0x7f0d0003" name="Animation.AppCompat.DropDownUp" type="style"/><public id="0x7f0d0004" name="AppTheme" type="style"/><public id="0x7f0d0005" name="Base.AlertDialog.AppCompat" type="style"/><public id="0x7f0d0006" name="Base.AlertDialog.AppCompat.Light" type="style"/><public id="0x7f0d0007" name="Base.Animation.AppCompat.Dialog" type="style"/><public id="0x7f0d0008" name="Base.Animation.AppCompat.DropDownUp" type="style"/><public id="0x7f0d0009" name="Base.DialogWindowTitle.AppCompat" type="style"/><public id="0x7f0d000a" name="Base.DialogWindowTitleBackground.AppCompat" type="style"/><public id="0x7f0d000b" name="Base.TextAppearance.AppCompat" type="style"/><public id="0x7f0d000c" name="Base.TextAppearance.AppCompat.Body1" type="style"/><public id="0x7f0d000d" name="Base.TextAppearance.AppCompat.Body2" type="style"/><public id="0x7f0d000e" name="Base.TextAppearance.AppCompat.Button" type="style"/><public id="0x7f0d000f" name="Base.TextAppearance.AppCompat.Caption" type="style"/><public id="0x7f0d0010" name="Base.TextAppearance.AppCompat.Display1" type="style"/><public id="0x7f0d0011" name="Base.TextAppearance.AppCompat.Display2" type="style"/><public id="0x7f0d0012" name="Base.TextAppearance.AppCompat.Display3" type="style"/><public id="0x7f0d0013" name="Base.TextAppearance.AppCompat.Display4" type="style"/><public id="0x7f0d0014" name="Base.TextAppearance.AppCompat.Headline" type="style"/><public id="0x7f0d0015" name="Base.TextAppearance.AppCompat.Inverse" type="style"/><public id="0x7f0d0016" name="Base.TextAppearance.AppCompat.Large" type="style"/><public id="0x7f0d0017" name="Base.TextAppearance.AppCompat.Large.Inverse" type="style"/><public id="0x7f0d0018" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" type="style"/><public id="0x7f0d0019" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" type="style"/><public id="0x7f0d001a" name="Base.TextAppearance.AppCompat.Medium" type="style"/><public id="0x7f0d001b" name="Base.TextAppearance.AppCompat.Medium.Inverse" type="style"/><public id="0x7f0d001c" name="Base.TextAppearance.AppCompat.Menu" type="style"/><public id="0x7f0d001d" name="Base.TextAppearance.AppCompat.SearchResult" type="style"/><public id="0x7f0d001e" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" type="style"/><public id="0x7f0d001f" name="Base.TextAppearance.AppCompat.SearchResult.Title" type="style"/><public id="0x7f0d0020" name="Base.TextAppearance.AppCompat.Small" type="style"/><public id="0x7f0d0021" name="Base.TextAppearance.AppCompat.Small.Inverse" type="style"/><public id="0x7f0d0022" name="Base.TextAppearance.AppCompat.Subhead" type="style"/><public id="0x7f0d0023" name="Base.TextAppearance.AppCompat.Subhead.Inverse" type="style"/><public id="0x7f0d0024" name="Base.TextAppearance.AppCompat.Title" type="style"/><public id="0x7f0d0025" name="Base.TextAppearance.AppCompat.Title.Inverse" type="style"/><public id="0x7f0d0026" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" type="style"/><public id="0x7f0d0027" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" type="style"/><public id="0x7f0d0028" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" type="style"/><public id="0x7f0d0029" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" type="style"/><public id="0x7f0d002a" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" type="style"/><public id="0x7f0d002b" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" type="style"/><public id="0x7f0d002c" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" type="style"/><public id="0x7f0d002d" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" type="style"/><public id="0x7f0d002e" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" type="style"/><public id="0x7f0d002f" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" type="style"/><public id="0x7f0d0030" name="Base.TextAppearance.AppCompat.Widget.Switch" type="style"/><public id="0x7f0d0031" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" type="style"/><public id="0x7f0d0032" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" type="style"/><public id="0x7f0d0033" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" type="style"/><public id="0x7f0d0034" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" type="style"/><public id="0x7f0d0035" name="Base.Theme.AppCompat" type="style"/><public id="0x7f0d0036" name="Base.Theme.AppCompat.CompactMenu" type="style"/><public id="0x7f0d0037" name="Base.Theme.AppCompat.Dialog" type="style"/><public id="0x7f0d0038" name="Base.Theme.AppCompat.Dialog.Alert" type="style"/><public id="0x7f0d0039" name="Base.Theme.AppCompat.Dialog.FixedSize" type="style"/><public id="0x7f0d003a" name="Base.Theme.AppCompat.Dialog.MinWidth" type="style"/><public id="0x7f0d003b" name="Base.Theme.AppCompat.DialogWhenLarge" type="style"/><public id="0x7f0d003c" name="Base.Theme.AppCompat.Light" type="style"/><public id="0x7f0d003d" name="Base.Theme.AppCompat.Light.DarkActionBar" type="style"/><public id="0x7f0d003e" name="Base.Theme.AppCompat.Light.Dialog" type="style"/><public id="0x7f0d003f" name="Base.Theme.AppCompat.Light.Dialog.Alert" type="style"/><public id="0x7f0d0040" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" type="style"/><public id="0x7f0d0041" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" type="style"/><public id="0x7f0d0042" name="Base.Theme.AppCompat.Light.DialogWhenLarge" type="style"/><public id="0x7f0d0043" name="Base.ThemeOverlay.AppCompat" type="style"/><public id="0x7f0d0044" name="Base.ThemeOverlay.AppCompat.ActionBar" type="style"/><public id="0x7f0d0045" name="Base.ThemeOverlay.AppCompat.Dark" type="style"/><public id="0x7f0d0046" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" type="style"/><public id="0x7f0d0047" name="Base.ThemeOverlay.AppCompat.Light" type="style"/><public id="0x7f0d0048" name="Base.V11.Theme.AppCompat.Dialog" type="style"/><public id="0x7f0d0049" name="Base.V11.Theme.AppCompat.Light.Dialog" type="style"/><public id="0x7f0d004e" name="Base.V7.Theme.AppCompat" type="style"/><public id="0x7f0d004f" name="Base.V7.Theme.AppCompat.Dialog" type="style"/><public id="0x7f0d0050" name="Base.V7.Theme.AppCompat.Light" type="style"/><public id="0x7f0d0051" name="Base.V7.Theme.AppCompat.Light.Dialog" type="style"/><public id="0x7f0d0052" name="Base.Widget.AppCompat.ActionBar" type="style"/><public id="0x7f0d0053" name="Base.Widget.AppCompat.ActionBar.Solid" type="style"/><public id="0x7f0d0054" name="Base.Widget.AppCompat.ActionBar.TabBar" type="style"/><public id="0x7f0d0055" name="Base.Widget.AppCompat.ActionBar.TabText" type="style"/><public id="0x7f0d0056" name="Base.Widget.AppCompat.ActionBar.TabView" type="style"/><public id="0x7f0d0057" name="Base.Widget.AppCompat.ActionButton" type="style"/><public id="0x7f0d0058" name="Base.Widget.AppCompat.ActionButton.CloseMode" type="style"/><public id="0x7f0d0059" name="Base.Widget.AppCompat.ActionButton.Overflow" type="style"/><public id="0x7f0d005a" name="Base.Widget.AppCompat.ActionMode" type="style"/><public id="0x7f0d005b" name="Base.Widget.AppCompat.ActivityChooserView" type="style"/><public id="0x7f0d005c" name="Base.Widget.AppCompat.AutoCompleteTextView" type="style"/><public id="0x7f0d005d" name="Base.Widget.AppCompat.Button" type="style"/><public id="0x7f0d005e" name="Base.Widget.AppCompat.Button.Borderless" type="style"/><public id="0x7f0d005f" name="Base.Widget.AppCompat.Button.Borderless.Colored" type="style"/><public id="0x7f0d0060" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" type="style"/><public id="0x7f0d0061" name="Base.Widget.AppCompat.Button.Small" type="style"/><public id="0x7f0d0062" name="Base.Widget.AppCompat.ButtonBar" type="style"/><public id="0x7f0d0063" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" type="style"/><public id="0x7f0d0064" name="Base.Widget.AppCompat.CompoundButton.CheckBox" type="style"/><public id="0x7f0d0065" name="Base.Widget.AppCompat.CompoundButton.RadioButton" type="style"/><public id="0x7f0d0066" name="Base.Widget.AppCompat.CompoundButton.Switch" type="style"/><public id="0x7f0d0068" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" type="style"/><public id="0x7f0d0069" name="Base.Widget.AppCompat.DropDownItem.Spinner" type="style"/><public id="0x7f0d006a" name="Base.Widget.AppCompat.EditText" type="style"/><public id="0x7f0d006b" name="Base.Widget.AppCompat.Light.ActionBar" type="style"/><public id="0x7f0d006c" name="Base.Widget.AppCompat.Light.ActionBar.Solid" type="style"/><public id="0x7f0d006d" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" type="style"/><public id="0x7f0d006e" name="Base.Widget.AppCompat.Light.ActionBar.TabText" type="style"/><public id="0x7f0d006f" name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" type="style"/><public id="0x7f0d0070" name="Base.Widget.AppCompat.Light.ActionBar.TabView" type="style"/><public id="0x7f0d0071" name="Base.Widget.AppCompat.Light.PopupMenu" type="style"/><public id="0x7f0d0072" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" type="style"/><public id="0x7f0d0073" name="Base.Widget.AppCompat.ListPopupWindow" type="style"/><public id="0x7f0d0074" name="Base.Widget.AppCompat.ListView" type="style"/><public id="0x7f0d0075" name="Base.Widget.AppCompat.ListView.DropDown" type="style"/><public id="0x7f0d0076" name="Base.Widget.AppCompat.ListView.Menu" type="style"/><public id="0x7f0d0077" name="Base.Widget.AppCompat.PopupMenu" type="style"/><public id="0x7f0d0078" name="Base.Widget.AppCompat.PopupMenu.Overflow" type="style"/><public id="0x7f0d0079" name="Base.Widget.AppCompat.PopupWindow" type="style"/><public id="0x7f0d007a" name="Base.Widget.AppCompat.ProgressBar" type="style"/><public id="0x7f0d007b" name="Base.Widget.AppCompat.ProgressBar.Horizontal" type="style"/><public id="0x7f0d007c" name="Base.Widget.AppCompat.RatingBar" type="style"/><public id="0x7f0d007d" name="Base.Widget.AppCompat.SearchView" type="style"/><public id="0x7f0d007e" name="Base.Widget.AppCompat.SearchView.ActionBar" type="style"/><public id="0x7f0d007f" name="Base.Widget.AppCompat.Spinner" type="style"/><public id="0x7f0d0080" name="Base.Widget.AppCompat.Spinner.DropDown.ActionBar" type="style"/><public id="0x7f0d0081" name="Base.Widget.AppCompat.Spinner.Underlined" type="style"/><public id="0x7f0d0082" name="Base.Widget.AppCompat.TextView.SpinnerItem" type="style"/><public id="0x7f0d0083" name="Base.Widget.AppCompat.Toolbar" type="style"/><public id="0x7f0d0084" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" type="style"/><public id="0x7f0d0085" name="Platform.AppCompat" type="style"/><public id="0x7f0d0086" name="Platform.AppCompat.Light" type="style"/><public id="0x7f0d0087" name="Platform.ThemeOverlay.AppCompat.Dark" type="style"/><public id="0x7f0d0088" name="Platform.ThemeOverlay.AppCompat.Light" type="style"/><public id="0x7f0d0089" name="Platform.V11.AppCompat" type="style"/><public id="0x7f0d008a" name="Platform.V11.AppCompat.Light" type="style"/><public id="0x7f0d008b" name="Platform.V12.AppCompat" type="style"/><public id="0x7f0d008c" name="Platform.V12.AppCompat.Light" type="style"/><public id="0x7f0d008d" name="Platform.V14.AppCompat" type="style"/><public id="0x7f0d008e" name="Platform.V14.AppCompat.Light" type="style"/><public id="0x7f0d008f" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" type="style"/><public id="0x7f0d0090" name="RtlOverlay.Widget.AppCompat.ActionButton.Overflow" type="style"/><public id="0x7f0d0091" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" type="style"/><public id="0x7f0d0092" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" type="style"/><public id="0x7f0d0093" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" type="style"/><public id="0x7f0d0094" name="RtlOverlay.Widget.AppCompat.Search.DropDown" type="style"/><public id="0x7f0d0095" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" type="style"/><public id="0x7f0d0096" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" type="style"/><public id="0x7f0d0097" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" type="style"/><public id="0x7f0d0098" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" type="style"/><public id="0x7f0d0099" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" type="style"/><public id="0x7f0d009a" name="RtlOverlay.Widget.AppCompat.Toolbar.Button.Navigation" type="style"/><public id="0x7f0d009b" name="TextAppearance.AppCompat" type="style"/><public id="0x7f0d009c" name="TextAppearance.AppCompat.Body1" type="style"/><public id="0x7f0d009d" name="TextAppearance.AppCompat.Body2" type="style"/><public id="0x7f0d009e" name="TextAppearance.AppCompat.Button" type="style"/><public id="0x7f0d009f" name="TextAppearance.AppCompat.Caption" type="style"/><public id="0x7f0d00a0" name="TextAppearance.AppCompat.Display1" type="style"/><public id="0x7f0d00a1" name="TextAppearance.AppCompat.Display2" type="style"/><public id="0x7f0d00a2" name="TextAppearance.AppCompat.Display3" type="style"/><public id="0x7f0d00a3" name="TextAppearance.AppCompat.Display4" type="style"/><public id="0x7f0d00a4" name="TextAppearance.AppCompat.Headline" type="style"/><public id="0x7f0d00a5" name="TextAppearance.AppCompat.Inverse" type="style"/><public id="0x7f0d00a6" name="TextAppearance.AppCompat.Large" type="style"/><public id="0x7f0d00a7" name="TextAppearance.AppCompat.Large.Inverse" type="style"/><public id="0x7f0d00a8" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" type="style"/><public id="0x7f0d00a9" name="TextAppearance.AppCompat.Light.SearchResult.Title" type="style"/><public id="0x7f0d00aa" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" type="style"/><public id="0x7f0d00ab" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" type="style"/><public id="0x7f0d00ac" name="TextAppearance.AppCompat.Medium" type="style"/><public id="0x7f0d00ad" name="TextAppearance.AppCompat.Medium.Inverse" type="style"/><public id="0x7f0d00ae" name="TextAppearance.AppCompat.Menu" type="style"/><public id="0x7f0d00af" name="TextAppearance.AppCompat.SearchResult.Subtitle" type="style"/><public id="0x7f0d00b0" name="TextAppearance.AppCompat.SearchResult.Title" type="style"/><public id="0x7f0d00b1" name="TextAppearance.AppCompat.Small" type="style"/><public id="0x7f0d00b2" name="TextAppearance.AppCompat.Small.Inverse" type="style"/><public id="0x7f0d00b3" name="TextAppearance.AppCompat.Subhead" type="style"/><public id="0x7f0d00b4" name="TextAppearance.AppCompat.Subhead.Inverse" type="style"/><public id="0x7f0d00b5" name="TextAppearance.AppCompat.Title" type="style"/><public id="0x7f0d00b6" name="TextAppearance.AppCompat.Title.Inverse" type="style"/><public id="0x7f0d00b7" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" type="style"/><public id="0x7f0d00b8" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" type="style"/><public id="0x7f0d00b9" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" type="style"/><public id="0x7f0d00ba" name="TextAppearance.AppCompat.Widget.ActionBar.Title" type="style"/><public id="0x7f0d00bb" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" type="style"/><public id="0x7f0d00bc" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" type="style"/><public id="0x7f0d00bd" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" type="style"/><public id="0x7f0d00be" name="TextAppearance.AppCompat.Widget.ActionMode.Title" type="style"/><public id="0x7f0d00bf" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" type="style"/><public id="0x7f0d00c0" name="TextAppearance.AppCompat.Widget.DropDownItem" type="style"/><public id="0x7f0d00c1" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" type="style"/><public id="0x7f0d00c2" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" type="style"/><public id="0x7f0d00c3" name="TextAppearance.AppCompat.Widget.Switch" type="style"/><public id="0x7f0d00c4" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" type="style"/><public id="0x7f0d00c5" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" type="style"/><public id="0x7f0d00c6" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" type="style"/><public id="0x7f0d00c7" name="TextAppearance.Widget.AppCompat.Toolbar.Title" type="style"/><public id="0x7f0d00c8" name="Theme.AppCompat" type="style"/><public id="0x7f0d00c9" name="Theme.AppCompat.CompactMenu" type="style"/><public id="0x7f0d00ca" name="Theme.AppCompat.Dialog" type="style"/><public id="0x7f0d00cb" name="Theme.AppCompat.Dialog.Alert" type="style"/><public id="0x7f0d00cc" name="Theme.AppCompat.Dialog.MinWidth" type="style"/><public id="0x7f0d00cd" name="Theme.AppCompat.DialogWhenLarge" type="style"/><public id="0x7f0d00ce" name="Theme.AppCompat.Light" type="style"/><public id="0x7f0d00cf" name="Theme.AppCompat.Light.DarkActionBar" type="style"/><public id="0x7f0d00d0" name="Theme.AppCompat.Light.Dialog" type="style"/><public id="0x7f0d00d1" name="Theme.AppCompat.Light.Dialog.Alert" type="style"/><public id="0x7f0d00d2" name="Theme.AppCompat.Light.Dialog.MinWidth" type="style"/><public id="0x7f0d00d3" name="Theme.AppCompat.Light.DialogWhenLarge" type="style"/><public id="0x7f0d00d4" name="Theme.AppCompat.Light.NoActionBar" type="style"/><public id="0x7f0d00d5" name="Theme.AppCompat.NoActionBar" type="style"/><public id="0x7f0d00d6" name="ThemeOverlay.AppCompat" type="style"/><public id="0x7f0d00d7" name="ThemeOverlay.AppCompat.ActionBar" type="style"/><public id="0x7f0d00d8" name="ThemeOverlay.AppCompat.Dark" type="style"/><public id="0x7f0d00d9" name="ThemeOverlay.AppCompat.Dark.ActionBar" type="style"/><public id="0x7f0d00da" name="ThemeOverlay.AppCompat.Light" type="style"/><public id="0x7f0d00db" name="Widget.AppCompat.ActionBar" type="style"/><public id="0x7f0d00dc" name="Widget.AppCompat.ActionBar.Solid" type="style"/><public id="0x7f0d00dd" name="Widget.AppCompat.ActionBar.TabBar" type="style"/><public id="0x7f0d00de" name="Widget.AppCompat.ActionBar.TabText" type="style"/><public id="0x7f0d00df" name="Widget.AppCompat.ActionBar.TabView" type="style"/><public id="0x7f0d00e0" name="Widget.AppCompat.ActionButton" type="style"/><public id="0x7f0d00e1" name="Widget.AppCompat.ActionButton.CloseMode" type="style"/><public id="0x7f0d00e2" name="Widget.AppCompat.ActionButton.Overflow" type="style"/><public id="0x7f0d00e3" name="Widget.AppCompat.ActionMode" type="style"/><public id="0x7f0d00e4" name="Widget.AppCompat.ActivityChooserView" type="style"/><public id="0x7f0d00e5" name="Widget.AppCompat.AutoCompleteTextView" type="style"/><public id="0x7f0d00e6" name="Widget.AppCompat.Button" type="style"/><public id="0x7f0d00e7" name="Widget.AppCompat.Button.Borderless" type="style"/><public id="0x7f0d00e8" name="Widget.AppCompat.Button.Borderless.Colored" type="style"/><public id="0x7f0d00e9" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" type="style"/><public id="0x7f0d00ea" name="Widget.AppCompat.Button.Small" type="style"/><public id="0x7f0d00eb" name="Widget.AppCompat.ButtonBar" type="style"/><public id="0x7f0d00ec" name="Widget.AppCompat.ButtonBar.AlertDialog" type="style"/><public id="0x7f0d00ed" name="Widget.AppCompat.CompoundButton.CheckBox" type="style"/><public id="0x7f0d00ee" name="Widget.AppCompat.CompoundButton.RadioButton" type="style"/><public id="0x7f0d00ef" name="Widget.AppCompat.CompoundButton.Switch" type="style"/><public id="0x7f0d00f0" name="Widget.AppCompat.DrawerArrowToggle" type="style"/><public id="0x7f0d00f1" name="Widget.AppCompat.DropDownItem.Spinner" type="style"/><public id="0x7f0d00f2" name="Widget.AppCompat.EditText" type="style"/><public id="0x7f0d00f3" name="Widget.AppCompat.Light.ActionBar" type="style"/><public id="0x7f0d00f4" name="Widget.AppCompat.Light.ActionBar.Solid" type="style"/><public id="0x7f0d00f5" name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" type="style"/><public id="0x7f0d00f6" name="Widget.AppCompat.Light.ActionBar.TabBar" type="style"/><public id="0x7f0d00f7" name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" type="style"/><public id="0x7f0d00f8" name="Widget.AppCompat.Light.ActionBar.TabText" type="style"/><public id="0x7f0d00f9" name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" type="style"/><public id="0x7f0d00fa" name="Widget.AppCompat.Light.ActionBar.TabView" type="style"/><public id="0x7f0d00fb" name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" type="style"/><public id="0x7f0d00fc" name="Widget.AppCompat.Light.ActionButton" type="style"/><public id="0x7f0d00fd" name="Widget.AppCompat.Light.ActionButton.CloseMode" type="style"/><public id="0x7f0d00fe" name="Widget.AppCompat.Light.ActionButton.Overflow" type="style"/><public id="0x7f0d00ff" name="Widget.AppCompat.Light.ActionMode.Inverse" type="style"/><public id="0x7f0d0100" name="Widget.AppCompat.Light.ActivityChooserView" type="style"/><public id="0x7f0d0101" name="Widget.AppCompat.Light.AutoCompleteTextView" type="style"/><public id="0x7f0d0102" name="Widget.AppCompat.Light.DropDownItem.Spinner" type="style"/><public id="0x7f0d0103" name="Widget.AppCompat.Light.ListPopupWindow" type="style"/><public id="0x7f0d0104" name="Widget.AppCompat.Light.ListView.DropDown" type="style"/><public id="0x7f0d0105" name="Widget.AppCompat.Light.PopupMenu" type="style"/><public id="0x7f0d0106" name="Widget.AppCompat.Light.PopupMenu.Overflow" type="style"/><public id="0x7f0d0107" name="Widget.AppCompat.Light.SearchView" type="style"/><public id="0x7f0d0108" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" type="style"/><public id="0x7f0d0109" name="Widget.AppCompat.ListPopupWindow" type="style"/><public id="0x7f0d010a" name="Widget.AppCompat.ListView" type="style"/><public id="0x7f0d010b" name="Widget.AppCompat.ListView.DropDown" type="style"/><public id="0x7f0d010c" name="Widget.AppCompat.ListView.Menu" type="style"/><public id="0x7f0d010d" name="Widget.AppCompat.PopupMenu" type="style"/><public id="0x7f0d010e" name="Widget.AppCompat.PopupMenu.Overflow" type="style"/><public id="0x7f0d010f" name="Widget.AppCompat.PopupWindow" type="style"/><public id="0x7f0d0110" name="Widget.AppCompat.ProgressBar" type="style"/><public id="0x7f0d0111" name="Widget.AppCompat.ProgressBar.Horizontal" type="style"/><public id="0x7f0d0112" name="Widget.AppCompat.RatingBar" type="style"/><public id="0x7f0d0113" name="Widget.AppCompat.SearchView" type="style"/><public id="0x7f0d0114" name="Widget.AppCompat.SearchView.ActionBar" type="style"/><public id="0x7f0d0115" name="Widget.AppCompat.Spinner" type="style"/><public id="0x7f0d0116" name="Widget.AppCompat.Spinner.DropDown" type="style"/><public id="0x7f0d0117" name="Widget.AppCompat.Spinner.DropDown.ActionBar" type="style"/><public id="0x7f0d0118" name="Widget.AppCompat.Spinner.Underlined" type="style"/><public id="0x7f0d0119" name="Widget.AppCompat.TextView.SpinnerItem" type="style"/><public id="0x7f0d011a" name="Widget.AppCompat.Toolbar" type="style"/><public id="0x7f0d011b" name="Widget.AppCompat.Toolbar.Button.Navigation" type="style"/><public id="0x7f0d004a" name="Base.V21.Theme.AppCompat" type="style"/><public id="0x7f0d004b" name="Base.V21.Theme.AppCompat.Dialog" type="style"/><public id="0x7f0d004c" name="Base.V21.Theme.AppCompat.Light" type="style"/><public id="0x7f0d004d" name="Base.V21.Theme.AppCompat.Light.Dialog" type="style"/><public id="0x7f0f0000" name="splits0" type="xml"/></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values\strings.xml" qualifiers=""><string name="abc_action_bar_home_description">Navigate home</string><string name="abc_action_bar_home_description_format">%1$s, %2$s</string><string name="abc_action_bar_home_subtitle_description_format">%1$s, %2$s, %3$s</string><string name="abc_action_bar_up_description">Navigate up</string><string name="abc_action_menu_overflow_description">More options</string><string name="abc_action_mode_done">Done</string><string name="abc_activity_chooser_view_see_all">See all</string><string name="abc_activitychooserview_choose_application">Choose an app</string><string name="abc_search_hint">Search…</string><string name="abc_searchview_description_clear">Clear query</string><string name="abc_searchview_description_query">Search query</string><string name="abc_searchview_description_search">Search</string><string name="abc_searchview_description_submit">Submit query</string><string name="abc_searchview_description_voice">Voice search</string><string name="abc_shareactionprovider_share_with">Share with</string><string name="abc_shareactionprovider_share_with_application">Share with %s</string><string name="abc_toolbar_collapse_description">Collapse</string><string name="about_label">About</string><string name="action_settings">Settings</string><string name="ai1">Hal</string><string name="ai2">Shirka</string><string name="ai3">Eddie</string><string name="app_name">Domino</string><string name="blue">Blue</string><string name="button_new_game">New Game</string><string name="cancel">Cancel</string><string name="clear_debug">Clear</string><string name="close">Close</string><string name="connect">Connect</string><string name="connection_error">Connection error</string><string name="continue_label">Continue</string><string name="create_group">Create Game</string><string name="creator_team">Game Creator Team</string><string name="cuban_domino">Cuban Style Domino Game</string><string name="debug">Debug Data</string><string name="debug_info">Debug Info</string><string name="demo_mode">Demo Mode</string><string name="devices">Devices</string><string name="different_server_version">Different Server Version</string><string name="do_you_want_to_start">Do you want to start?</string><string name="domino_set">Domino Set</string><string name="empty_player_name">[Waiting]</string><string name="error">Error</string><string name="error_bluetooth_not_supported">Error. Bluetooth not supported</string><string name="error_getting_game_data">Error getting game data</string><string name="exit_game_msg">There is a game in progress. Do you want to quit?</string><string name="exit_label">Exit</string><string name="fast">Fast</string><string name="game_creator">Game Creator</string><string name="game_draw">Game is draw</string><string name="game_info">Score</string><string name="game_over">Game Over</string><string name="game_setup">Game Setup</string><string name="getting_game_data">Connecting</string><string name="hello">Hello World, DominoActivity!</string><string name="hello_world">Hello world!</string><string name="i_passed">I pass</string><string name="its_better_if_you_start">"It's better if you start"</string><string name="join_group">Join Game</string><string name="joining_game">Joining</string><string name="let_me_start">Let me start</string><string name="main_title">Domino</string><string name="max_score">Max Score</string><string name="me">Me</string><string name="must_select_device">Select a device to connect</string><string name="network">Network</string><string name="new_game">New Game</string><string name="new_game_label">New Game</string><string name="next_round">Next Round</string><string name="no">No</string><string name="no_devices">No devices</string><string name="no_let_me_start">No, let me start</string><string name="no_way_you_start">No way, you start</string><string name="normal">Normal</string><string name="ok">OK</string><string name="ok_i_will_start">"OK, I'll start"</string><string name="ok_you_start">OK, you start</string><string name="pale">Yellow</string><string name="pieces_28">28 pieces</string><string name="pieces_55">55 pieces</string><string name="player_passed">Player %s passed</string><string name="player_playing">Player %s is playing</string><string name="player_thinking">Player %s is thinking</string><string name="players">Players</string><string name="ready">Touch Start when you are ready</string><string name="ready_to_start">Ready to start</string><string name="retry">Retry</string><string name="round_over">Round Over</string><string name="scanning">Scanning...</string><string name="score">Score</string><string name="settings">Settings</string><string name="slow">Slow</string><string name="speed">Speed</string><string name="start">Start</string><string name="start_game">Start Game</string><string name="team_play">Team Play</string><string name="theme">Theme</string><string name="title_activity_connection">Select Device</string><string name="title_activity_debug">DebugActivity</string><string name="title_activity_domino">Domino</string><string name="unable_to_join">Unable to join game</string><string name="version">Version</string><string name="waiting_for_new_round">Waiting for round to start</string><string name="waiting_for_players">Waiting for players</string><string name="winner_player">Winner: %s</string><string name="yes">Yes</string><string name="your_name">Name</string><string name="your_name_title">Your Name</string><string name="your_partner_doesnt_wants_to_start">"Your partner doesn't want to start"</string><string name="your_partner_wants_to_start">Your partner wants to start</string></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values\styles.xml" qualifiers=""><style name="AlertDialog.AppCompat" parent="@style/Base.AlertDialog.AppCompat"/><style name="AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat.Light"/><style name="Animation.AppCompat.Dialog" parent="@style/Base.Animation.AppCompat.Dialog"/><style name="Animation.AppCompat.DropDownUp" parent="@style/Base.Animation.AppCompat.DropDownUp"/><style name="AppTheme" parent="@style/Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/domino_primary</item>
        <item name="colorPrimaryDark">@color/domino_primary_dark</item>
        <item name="colorAccent">@color/domino_accent</item>
        <item name="android:windowBackground">@color/domino_background</item>
        <item name="android:textColorPrimary">@color/domino_text_primary</item>
        <item name="android:textColorSecondary">@color/domino_text_secondary</item>
    </style><style name="DominoButton" parent="@style/Widget.AppCompat.Button">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">12dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:layout_margin">4dp</item>
    </style><style name="DominoButton.Positive">
        <item name="android:background">@color/domino_button_positive</item>
    </style><style name="DominoButton.Negative">
        <item name="android:background">@color/domino_button_negative</item>
    </style><style name="DominoButton.Neutral">
        <item name="android:background">@color/domino_button_neutral</item>
    </style><style name="DominoText" parent="@android:style/Widget.TextView">
        <item name="android:textColor">@color/domino_text_primary</item>
        <item name="android:textSize">14sp</item>
    </style><style name="DominoText.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">16dp</item>
    </style><style name="DominoText.Score">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:background">@color/domino_score_background</item>
        <item name="android:padding">8dp</item>
        <item name="android:gravity">center</item>
    </style><style name="DominoCard" parent="@android:style/Widget">
        <item name="android:background">@color/domino_surface</item>
        <item name="android:elevation">4dp</item>
        <item name="android:padding">16dp</item>
        <item name="android:layout_margin">8dp</item>
    </style><style name="Base.AlertDialog.AppCompat" parent="@android:style/Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
    </style><style name="Base.AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat"/><style name="Base.Animation.AppCompat.Dialog" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style><style name="Base.Animation.AppCompat.DropDownUp" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style><style name="Base.DialogWindowTitle.AppCompat" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
    </style><style name="Base.DialogWindowTitleBackground.AppCompat" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?dialogPreferredPadding</item>
    </style><style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHighlight">?android:textColorHighlight</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorLink">?android:textColorLink</item>
    </style><style name="Base.TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textAllCaps">true</item>
    </style><style name="Base.TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Menu"/><style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Menu"/><style name="Base.TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium">
        <item name="android:textColor">?android:textColorSecondaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style><style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style><style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">18.0sp</item>
    </style><style name="Base.TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:textColorTertiary</item>
    </style><style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small">
        <item name="android:textColor">?android:textColorTertiaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textColor">?android:textColorSecondaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style><style name="Base.TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@android:style/TextAppearance.Small">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?actionMenuTextColor</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">?android:textColorPrimaryDisableOnly</item>
    </style><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Menu"/><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Menu"/><style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@style/TextAppearance.AppCompat.Button"/><style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/TextAppearance.AppCompat.Menu"/><style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@android:style/TextAppearance.Medium">
        <item name="android:textColor">?android:textColorPrimaryDisableOnly</item>
    </style><style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/><style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title"/><style name="Base.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat"/><style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:textAppearanceMedium</item>
    </style><style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V11.Theme.AppCompat.Dialog"/><style name="Base.Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowFixedHeightMajor">@dimen/dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/dialog_fixed_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.DialogWhenLarge" parent="@style/Theme.AppCompat"/><style name="Base.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light"/><style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style><style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V11.Theme.AppCompat.Light.Dialog"/><style name="Base.Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Light.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/dialog_fixed_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style><style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Theme.AppCompat.Light"/><style name="Base.ThemeOverlay.AppCompat" parent=""/><style name="Base.ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="colorControlNormal">?android:textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style><style name="Base.ThemeOverlay.AppCompat.Dark" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="android:colorForeground">@color/bright_foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/hint_foreground_material_light</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/hint_foreground_material_dark</item>
        <item name="android:textColorLink">@color/link_text_material_dark</item>
        <item name="android:colorForegroundInverse">@color/bright_foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="isLightTheme">false</item>
    </style><style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
        <item name="colorControlNormal">?android:textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style><style name="Base.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:colorForeground">@color/bright_foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/hint_foreground_material_dark</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/hint_foreground_material_light</item>
        <item name="android:textColorLink">@color/link_text_material_light</item>
        <item name="android:colorForegroundInverse">@color/bright_foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="isLightTheme">true</item>
    </style><style name="Base.V11.Theme.AppCompat.Dialog" parent="@style/Base.V7.Theme.AppCompat.Dialog">
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">@bool/abc_config_closeDialogWhenTouchOutside</item>
    </style><style name="Base.V11.Theme.AppCompat.Light.Dialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">@bool/abc_config_closeDialogWhenTouchOutside</item>
    </style><style name="Base.V7.Theme.AppCompat" parent="@style/Platform.AppCompat">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Button</item>
        <item name="actionBarDivider">?dividerVertical</item>
        <item name="actionBarItemBackground">?selectableItemBackgroundBorderless</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@null</item>
        <item name="actionModeCopyDrawable">@null</item>
        <item name="actionModeCutDrawable">@null</item>
        <item name="actionModePasteDrawable">@null</item>
        <item name="actionModeSelectAllDrawable">@null</item>
        <item name="actionModeShareDrawable">@null</item>
        <item name="actionModeSplitBackground">?colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogTheme">@style/Theme.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlActivated">?colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/Theme.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@null</item>
        <item name="dividerVertical">@null</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="dropdownListPreferredItemHeight">?listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@null</item>
        <item name="isLightTheme">false</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64.0dip</item>
        <item name="listPreferredItemHeightLarge">80.0dip</item>
        <item name="listPreferredItemHeightSmall">48.0dip</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@null</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowFixedHeightMajor">0.0dip</item>
        <item name="windowFixedHeightMinor">0.0dip</item>
        <item name="windowFixedWidthMajor">0.0dip</item>
        <item name="windowFixedWidthMinor">0.0dip</item>
    </style><style name="Base.V7.Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat">
        <item name="android:colorBackground">@color/background_floating_material_dark</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background_dark</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/Base.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style><style name="Base.V7.Theme.AppCompat.Light" parent="@style/Platform.AppCompat.Light">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Button</item>
        <item name="actionBarDivider">?dividerVertical</item>
        <item name="actionBarItemBackground">?selectableItemBackgroundBorderless</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@null</item>
        <item name="actionModeCopyDrawable">@null</item>
        <item name="actionModeCutDrawable">@null</item>
        <item name="actionModePasteDrawable">@null</item>
        <item name="actionModeSelectAllDrawable">@null</item>
        <item name="actionModeShareDrawable">@null</item>
        <item name="actionModeSplitBackground">?colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogTheme">@style/Theme.AppCompat.Light.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlActivated">?colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/Theme.AppCompat.Light.Dialog</item>
        <item name="dividerHorizontal">@null</item>
        <item name="dividerVertical">@null</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="dropdownListPreferredItemHeight">?listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@null</item>
        <item name="isLightTheme">true</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64.0dip</item>
        <item name="listPreferredItemHeightLarge">80.0dip</item>
        <item name="listPreferredItemHeightSmall">48.0dip</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@null</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowFixedHeightMajor">0.0dip</item>
        <item name="windowFixedHeightMinor">0.0dip</item>
        <item name="windowFixedWidthMajor">0.0dip</item>
        <item name="windowFixedWidthMinor">0.0dip</item>
    </style><style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">@color/background_floating_material_light</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background_light</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/Base.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style><style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="android:gravity">center_vertical</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="background">@null</item>
        <item name="backgroundSplit">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="displayOptions">showTitle</item>
        <item name="divider">?dividerVertical</item>
        <item name="elevation">8.0dip</item>
        <item name="height">?actionBarSize</item>
        <item name="popupTheme">?actionBarPopupTheme</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
    </style><style name="Base.Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="background">?colorPrimary</item>
        <item name="backgroundSplit">?colorPrimary</item>
        <item name="backgroundStacked">?colorPrimary</item>
    </style><style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?actionBarDivider</item>
        <item name="dividerPadding">8.0dip</item>
        <item name="showDividers">middle</item>
    </style><style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:maxWidth">180.0dip</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">true</item>
    </style><style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:gravity">center_horizontal</item>
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:paddingLeft">16.0dip</item>
        <item name="android:paddingRight">16.0dip</item>
        <item name="android:layout_width">0.0dip</item>
        <item name="android:minWidth">80.0dip</item>
        <item name="android:layout_weight">1.0</item>
    </style><style name="Base.Widget.AppCompat.ActionButton" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">?actionBarItemBackground</item>
        <item name="android:paddingLeft">12.0dip</item>
        <item name="android:paddingRight">12.0dip</item>
        <item name="android:scaleType">center</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style><style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:background">?selectableItemBackgroundBorderless</item>
        <item name="android:minWidth">56.0dip</item>
    </style><style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:background">?actionBarItemBackground</item>
        <item name="android:src">@null</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
    </style><style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?actionModeBackground</item>
        <item name="backgroundSplit">?actionModeSplitBackground</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
        <item name="height">?actionBarSize</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
    </style><style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@null</item>
        <item name="divider">?dividerVertical</item>
        <item name="dividerPadding">6.0dip</item>
        <item name="showDividers">middle</item>
    </style><style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.AutoCompleteTextView">
        <item name="android:textAppearance">?android:textAppearanceMediumInverse</item>
        <item name="android:textColor">?editTextColor</item>
        <item name="android:background">?editTextBackground</item>
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@null</item>
    </style><style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget">
        <item name="android:textAppearance">?android:textAppearanceButton</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:minWidth">88.0dip</item>
        <item name="android:minHeight">48.0dip</item>
    </style><style name="Base.Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style><style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless">
        <item name="android:textColor">?colorAccent</item>
    </style><style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64.0dip</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
        <item name="android:maxLines">2</item>
    </style><style name="Base.Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:minWidth">48.0dip</item>
        <item name="android:minHeight">48.0dip</item>
    </style><style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget">
        <item name="android:background">@null</item>
    </style><style name="Base.Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar">
        <item name="android:background">@null</item>
    </style><style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">?selectableItemBackgroundBorderless</item>
        <item name="android:button">?android:listChoiceIndicatorMultiple</item>
    </style><style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.CompoundButton.RadioButton">
        <item name="android:background">?selectableItemBackgroundBorderless</item>
        <item name="android:button">?android:listChoiceIndicatorSingle</item>
    </style><style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="@android:style/Widget.CompoundButton">
        <item name="android:background">?selectableItemBackgroundBorderless</item>
        <item name="android:padding">@dimen/abc_switch_padding</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="showText">false</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="track">@null</item>
    </style><style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="color">?android:textColorSecondary</item>
        <item name="middleBarArrowSize">16.0dip</item>
        <item name="spinBars">true</item>
        <item name="thickness">2.0dip</item>
        <item name="topBottomBarArrowSize">11.309998dip</item>
    </style><style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">8.0dip</item>
        <item name="android:paddingRight">8.0dip</item>
    </style><style name="Base.Widget.AppCompat.EditText" parent="@android:style/Widget.EditText">
        <item name="android:textAppearance">?android:textAppearanceMediumInverse</item>
        <item name="android:textColor">?editTextColor</item>
        <item name="android:background">?editTextBackground</item>
    </style><style name="Base.Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
        <item name="background">?colorPrimary</item>
        <item name="backgroundSplit">?colorPrimary</item>
        <item name="backgroundStacked">?colorPrimary</item>
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar"/><style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText"/><style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style><style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style><style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow"/><style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="overlapAnchor">true</item>
    </style><style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@null</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:dropDownHorizontalOffset">0.0dip</item>
        <item name="android:dropDownVerticalOffset">0.0dip</item>
    </style><style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.ListView">
        <item name="android:listSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:divider">?dividerHorizontal</item>
    </style><style name="Base.Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView">
        <item name="android:divider">@null</item>
    </style><style name="Base.Widget.AppCompat.ListView.Menu" parent="@android:style/Widget.ListView.Menu">
        <item name="android:listSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:divider">?dividerHorizontal</item>
    </style><style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow"/><style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="overlapAnchor">true</item>
    </style><style name="Base.Widget.AppCompat.PopupWindow" parent="@android:style/Widget.PopupWindow"/><style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Holo.ProgressBar"/><style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Holo.ProgressBar.Horizontal"/><style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.RatingBar">
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_full_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_full_material</item>
    </style><style name="Base.Widget.AppCompat.SearchView" parent="@android:style/Widget">
        <item name="closeIcon">@null</item>
        <item name="commitIcon">@null</item>
        <item name="goIcon">@null</item>
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="searchHintIcon">@null</item>
        <item name="searchIcon">@null</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
        <item name="voiceIcon">@null</item>
    </style><style name="Base.Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView">
        <item name="queryBackground">@null</item>
        <item name="queryHint">@string/abc_search_hint</item>
        <item name="searchHintIcon">@null</item>
        <item name="submitBackground">@null</item>
    </style><style name="Base.Widget.AppCompat.Spinner" parent="@android:style/Widget.Holo.Spinner">
        <item name="android:background">@null</item>
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@null</item>
    </style><style name="Base.Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@android:style/Widget">
        <item name="android:gravity">start|center</item>
        <item name="android:background">@null</item>
        <item name="android:clickable">true</item>
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@null</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:dropDownHorizontalOffset">0.0dip</item>
        <item name="android:dropDownVerticalOffset">0.0dip</item>
        <item name="disableChildrenWhenDisabled">true</item>
        <item name="overlapAnchor">true</item>
        <item name="popupPromptView">@layout/abc_simple_dropdown_hint</item>
        <item name="spinnerMode">dropdown</item>
    </style><style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style><style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8.0dip</item>
        <item name="android:paddingRight">8.0dip</item>
    </style><style name="Base.Widget.AppCompat.Toolbar" parent="@android:style/Widget">
        <item name="android:minHeight">?actionBarSize</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="collapseIcon">?homeAsUpIndicator</item>
        <item name="contentInsetStart">16.0dip</item>
        <item name="maxButtonHeight">56.0dip</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="titleMargins">4.0dip</item>
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
    </style><style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget">
        <item name="android:background">?selectableItemBackground</item>
        <item name="android:scaleType">center</item>
        <item name="android:minWidth">56.0dip</item>
    </style><style name="Platform.AppCompat" parent="@style/Platform.V14.AppCompat"/><style name="Platform.AppCompat.Light" parent="@style/Platform.V14.AppCompat.Light"/><style name="Platform.ThemeOverlay.AppCompat.Dark" parent="">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
    </style><style name="Platform.ThemeOverlay.AppCompat.Light" parent="">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
    </style><style name="Platform.V11.AppCompat" parent="@android:style/Theme.Holo">
        <item name="android:colorForeground">@color/bright_foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/hint_foreground_material_light</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/hint_foreground_material_dark</item>
        <item name="android:textColorLink">@color/link_text_material_dark</item>
        <item name="android:colorForegroundInverse">@color/bright_foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLinkInverse">@color/link_text_material_light</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
    </style><style name="Platform.V11.AppCompat.Light" parent="@android:style/Theme.Holo.Light">
        <item name="android:colorForeground">@color/bright_foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/hint_foreground_material_dark</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/hint_foreground_material_light</item>
        <item name="android:textColorLink">@color/link_text_material_light</item>
        <item name="android:colorForegroundInverse">@color/bright_foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLinkInverse">@color/link_text_material_dark</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
    </style><style name="Platform.V12.AppCompat" parent="@style/Platform.V11.AppCompat">
        <item name="android:textCursorDrawable">@null</item>
    </style><style name="Platform.V12.AppCompat.Light" parent="@style/Platform.V11.AppCompat.Light">
        <item name="android:textCursorDrawable">@null</item>
    </style><style name="Platform.V14.AppCompat" parent="@style/Platform.V12.AppCompat">
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style><style name="Platform.V14.AppCompat.Light" parent="@style/Platform.V12.AppCompat.Light">
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style><style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="@android:style/Widget">
        <item name="android:layout_gravity">center|left</item>
        <item name="android:paddingRight">8.0dip</item>
    </style><style name="RtlOverlay.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton.Overflow">
        <item name="android:paddingLeft">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="@android:style/Widget">
        <item name="android:paddingRight">16.0dip</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="@android:style/Widget">
        <item name="android:layout_marginLeft">16.0dip</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="@android:style/Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="@android:style/Widget">
        <item name="android:paddingLeft">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingRight">4.0dip</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="@android:style/Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="@android:style/Widget">
        <item name="android:layout_toLeftOf">@id/edit_query</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="@android:style/Widget">
        <item name="android:layout_alignParentRight">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="@style/Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toLeftOf">@android:id/icon2</item>
        <item name="android:layout_toRightOf">@android:id/icon1</item>
    </style><style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="@android:style/Widget">
        <item name="android:layout_marginLeft">@dimen/abc_dropdownitem_text_padding_left</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/Base.Widget.AppCompat.Toolbar.Button.Navigation">
        <item name="android:paddingLeft">@dimen/abc_action_bar_navigation_padding_start_material</item>
    </style><style name="TextAppearance.AppCompat" parent="@style/Base.TextAppearance.AppCompat"/><style name="TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat.Body1"/><style name="TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat.Body2"/><style name="TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat.Button"/><style name="TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat.Caption"/><style name="TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat.Display1"/><style name="TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat.Display2"/><style name="TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat.Display3"/><style name="TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat.Display4"/><style name="TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat.Headline"/><style name="TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat.Inverse"/><style name="TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat.Large"/><style name="TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large.Inverse"/><style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="@style/TextAppearance.AppCompat.SearchResult.Subtitle"/><style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="@style/TextAppearance.AppCompat.SearchResult.Title"/><style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large"/><style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small"/><style name="TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat.Medium"/><style name="TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium.Inverse"/><style name="TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat.Menu"/><style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Subtitle"/><style name="TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Title"/><style name="TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat.Small"/><style name="TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small.Inverse"/><style name="TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat.Subhead"/><style name="TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead.Inverse"/><style name="TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat.Title"/><style name="TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title.Inverse"/><style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu"/><style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/><style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse"/><style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title"/><style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse"/><style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/><style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/><style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title"/><style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Title"/><style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="@style/Base.TextAppearance.AppCompat.Widget.DropDownItem"/><style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large"/><style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small"/><style name="TextAppearance.AppCompat.Widget.Switch" parent="@style/Base.TextAppearance.AppCompat.Widget.Switch"/><style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem"/><style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item"/><style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle"/><style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title"/><style name="Theme.AppCompat" parent="@style/Base.Theme.AppCompat"/><style name="Theme.AppCompat.CompactMenu" parent="@style/Base.Theme.AppCompat.CompactMenu"/><style name="Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat.Dialog"/><style name="Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog.Alert"/><style name="Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog.MinWidth"/><style name="Theme.AppCompat.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.DialogWhenLarge"/><style name="Theme.AppCompat.Light" parent="@style/Base.Theme.AppCompat.Light"/><style name="Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light.DarkActionBar"/><style name="Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light.Dialog"/><style name="Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog.Alert"/><style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog.MinWidth"/><style name="Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.Light.DialogWhenLarge"/><style name="Theme.AppCompat.Light.NoActionBar" parent="@style/Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.AppCompat.NoActionBar" parent="@style/Theme.AppCompat">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="ThemeOverlay.AppCompat" parent="@style/Base.ThemeOverlay.AppCompat"/><style name="ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.ActionBar"/><style name="ThemeOverlay.AppCompat.Dark" parent="@style/Base.ThemeOverlay.AppCompat.Dark"/><style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="ThemeOverlay.AppCompat.Light" parent="@style/Base.ThemeOverlay.AppCompat.Light"/><style name="Widget.AppCompat.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar"/><style name="Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar.Solid"/><style name="Widget.AppCompat.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar"/><style name="Widget.AppCompat.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText"/><style name="Widget.AppCompat.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView"/><style name="Widget.AppCompat.ActionButton" parent="@style/Base.Widget.AppCompat.ActionButton"/><style name="Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton.CloseMode"/><style name="Widget.AppCompat.ActionButton.Overflow" parent="@style/RtlOverlay.Widget.AppCompat.ActionButton.Overflow"/><style name="Widget.AppCompat.ActionMode" parent="@style/Base.Widget.AppCompat.ActionMode"/><style name="Widget.AppCompat.ActivityChooserView" parent="@style/Base.Widget.AppCompat.ActivityChooserView"/><style name="Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.Widget.AppCompat.AutoCompleteTextView"/><style name="Widget.AppCompat.Button" parent="@style/Base.Widget.AppCompat.Button"/><style name="Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button.Borderless"/><style name="Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless.Colored"/><style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog"/><style name="Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button.Small"/><style name="Widget.AppCompat.ButtonBar" parent="@style/Base.Widget.AppCompat.ButtonBar"/><style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar.AlertDialog"/><style name="Widget.AppCompat.CompoundButton.CheckBox" parent="@style/Base.Widget.AppCompat.CompoundButton.CheckBox"/><style name="Widget.AppCompat.CompoundButton.RadioButton" parent="@style/Base.Widget.AppCompat.CompoundButton.RadioButton"/><style name="Widget.AppCompat.CompoundButton.Switch" parent="@style/Base.Widget.AppCompat.CompoundButton.Switch"/><style name="Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?colorControlNormal</item>
    </style><style name="Widget.AppCompat.DropDownItem.Spinner" parent="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text"/><style name="Widget.AppCompat.EditText" parent="@style/Base.Widget.AppCompat.EditText"/><style name="Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar"/><style name="Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar.Solid"/><style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.Solid"/><style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabBar"/><style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabBar"/><style name="Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText"/><style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse"/><style name="Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabView"/><style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabView"/><style name="Widget.AppCompat.Light.ActionButton" parent="@style/Widget.AppCompat.ActionButton"/><style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="@style/Widget.AppCompat.ActionButton.CloseMode"/><style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="@style/Widget.AppCompat.ActionButton.Overflow"/><style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="@style/Widget.AppCompat.ActionMode"/><style name="Widget.AppCompat.Light.ActivityChooserView" parent="@style/Widget.AppCompat.ActivityChooserView"/><style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="@style/Widget.AppCompat.AutoCompleteTextView"/><style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="@style/Widget.AppCompat.DropDownItem.Spinner"/><style name="Widget.AppCompat.Light.ListPopupWindow" parent="@style/Widget.AppCompat.ListPopupWindow"/><style name="Widget.AppCompat.Light.ListView.DropDown" parent="@style/Widget.AppCompat.ListView.DropDown"/><style name="Widget.AppCompat.Light.PopupMenu" parent="@style/Base.Widget.AppCompat.Light.PopupMenu"/><style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu.Overflow"/><style name="Widget.AppCompat.Light.SearchView" parent="@style/Widget.AppCompat.SearchView"/><style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown.ActionBar"/><style name="Widget.AppCompat.ListPopupWindow" parent="@style/Base.Widget.AppCompat.ListPopupWindow"/><style name="Widget.AppCompat.ListView" parent="@style/Base.Widget.AppCompat.ListView"/><style name="Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView.DropDown"/><style name="Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView.Menu"/><style name="Widget.AppCompat.PopupMenu" parent="@style/Base.Widget.AppCompat.PopupMenu"/><style name="Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu.Overflow"/><style name="Widget.AppCompat.PopupWindow" parent="@style/Base.Widget.AppCompat.PopupWindow"/><style name="Widget.AppCompat.ProgressBar" parent="@style/Base.Widget.AppCompat.ProgressBar"/><style name="Widget.AppCompat.ProgressBar.Horizontal" parent="@style/Base.Widget.AppCompat.ProgressBar.Horizontal"/><style name="Widget.AppCompat.RatingBar" parent="@style/Base.Widget.AppCompat.RatingBar"/><style name="Widget.AppCompat.SearchView" parent="@style/Base.Widget.AppCompat.SearchView"/><style name="Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView.ActionBar"/><style name="Widget.AppCompat.Spinner" parent="@style/Base.Widget.AppCompat.Spinner"/><style name="Widget.AppCompat.Spinner.DropDown" parent="@style/Widget.AppCompat.Spinner"/><style name="Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@style/Base.Widget.AppCompat.Spinner.DropDown.ActionBar"/><style name="Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner.Underlined"/><style name="Widget.AppCompat.TextView.SpinnerItem" parent="@style/Base.Widget.AppCompat.TextView.SpinnerItem"/><style name="Widget.AppCompat.Toolbar" parent="@style/Base.Widget.AppCompat.Toolbar"/><style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/RtlOverlay.Widget.AppCompat.Toolbar.Button.Navigation"/></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-ar\strings.xml" qualifiers="ar"><string name="abc_action_bar_home_description">Navigate home</string><string name="abc_action_bar_home_description_format">%1$s, %2$s</string><string name="abc_action_bar_home_subtitle_description_format">%1$s, %2$s, %3$s</string><string name="abc_action_bar_up_description">Navigate up</string><string name="abc_action_menu_overflow_description">More options</string><string name="abc_action_mode_done">Done</string><string name="abc_activity_chooser_view_see_all">See all</string><string name="abc_activitychooserview_choose_application">Choose an app</string><string name="abc_capital_off">OFF</string><string name="abc_capital_on">ON</string><string name="abc_search_hint">Search…</string><string name="abc_searchview_description_clear">Clear query</string><string name="abc_searchview_description_query">Search query</string><string name="abc_searchview_description_search">Search</string><string name="abc_searchview_description_submit">Submit query</string><string name="abc_searchview_description_voice">Voice search</string><string name="abc_shareactionprovider_share_with">Share with</string><string name="abc_shareactionprovider_share_with_application">Share with %s</string><string name="abc_toolbar_collapse_description">Collapse</string><string name="about_label">حول</string><string name="action_settings">الإعدادات</string><string name="ai1">هال</string><string name="ai2">شيركا</string><string name="ai3">إيدي</string><string name="app_name">دومينو</string><string name="blue">أزرق</string><string name="button_new_game">لعبة جديدة</string><string name="cancel">إلغاء</string><string name="clear_debug">مسح</string><string name="close">إغلاق</string><string name="connect">اتصال</string><string name="connection_error">خطأ في الاتصال</string><string name="continue_label">متابعة</string><string name="create_group">إنشاء لعبة</string><string name="creator_team">فريق منشئ اللعبة</string><string name="cuban_domino">لعبة دومينو كوبية الطراز</string><string name="debug">بيانات التصحيح</string><string name="debug_info">معلومات التصحيح</string><string name="demo_mode">وضع العرض التوضيحي</string><string name="devices">الأجهزة</string><string name="different_server_version">إصدار خادم مختلف</string><string name="do_you_want_to_start">هل تريد أن تبدأ؟</string><string name="domino_set">مجموعة الدومينو</string><string name="empty_player_name">[في الانتظار]</string><string name="error">خطأ</string><string name="error_bluetooth_not_supported">خطأ. البلوتوث غير مدعوم</string><string name="error_getting_game_data">خطأ في الحصول على بيانات اللعبة</string><string name="exit_game_msg">هناك لعبة قيد التشغيل. هل تريد الخروج؟</string><string name="exit_label">خروج</string><string name="fast">سريع</string><string name="game_creator">منشئ اللعبة</string><string name="game_draw">اللعبة متعادلة</string><string name="game_info">النتيجة</string><string name="game_over">انتهت اللعبة</string><string name="game_setup">إعداد اللعبة</string><string name="getting_game_data">جاري الاتصال</string><string name="hello">مرحبا بالعالم، نشاط الدومينو!</string><string name="hello_world">مرحبا بالعالم!</string><string name="i_passed">مررت</string><string name="its_better_if_you_start">"من الأفضل أن تبدأ أنت"</string><string name="join_group">انضمام للعبة</string><string name="joining_game">جاري الانضمام</string><string name="let_me_start">دعني أبدأ</string><string name="main_title">دومينو</string><string name="max_score">أقصى نتيجة</string><string name="me">أنا</string><string name="must_select_device">اختر جهازاً للاتصال</string><string name="network">الشبكة</string><string name="new_game">لعبة جديدة</string><string name="new_game_label">لعبة جديدة</string><string name="next_round">الجولة التالية</string><string name="no">لا</string><string name="no_devices">لا توجد أجهزة</string><string name="no_let_me_start">لا، دعني أبدأ</string><string name="no_way_you_start">لا، ابدأ أنت</string><string name="normal">عادي</string><string name="ok">موافق</string><string name="ok_i_will_start">"حسناً، سأبدأ"</string><string name="ok_you_start">حسناً، ابدأ أنت</string><string name="pale">أصفر</string><string name="pieces_28">28 قطعة</string><string name="pieces_55">55 قطعة</string><string name="player_passed">اللاعب %s مرر</string><string name="player_playing">اللاعب %s يلعب</string><string name="player_thinking">اللاعب %s يفكر</string><string name="players">اللاعبون</string><string name="ready">اضغط ابدأ عندما تكون جاهزاً</string><string name="ready_to_start">جاهز للبدء</string><string name="retry">إعادة المحاولة</string><string name="round_over">انتهت الجولة</string><string name="scanning">جاري البحث...</string><string name="score">النتيجة</string><string name="settings">الإعدادات</string><string name="slow">بطيء</string><string name="speed">السرعة</string><string name="start">ابدأ</string><string name="start_game">بدء اللعبة</string><string name="team_play">لعب جماعي</string><string name="theme">المظهر</string><string name="title_activity_connection">اختيار الجهاز</string><string name="title_activity_debug">نشاط التصحيح</string><string name="title_activity_domino">دومينو</string><string name="unable_to_join">غير قادر على الانضمام للعبة</string><string name="version">الإصدار</string><string name="waiting_for_new_round">في انتظار بدء الجولة</string><string name="waiting_for_players">في انتظار اللاعبين</string><string name="winner_player">الفائز: %s</string><string name="yes">نعم</string><string name="your_name">الاسم</string><string name="your_name_title">اسمك</string><string name="your_partner_doesnt_wants_to_start">"شريكك لا يريد أن يبدأ"</string><string name="your_partner_wants_to_start">شريكك يريد أن يبدأ</string></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-h720dp\dimens.xml" qualifiers="h720dp-v13"><dimen name="abc_alert_dialog_button_bar_height">54.0dip</dimen></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-land\bools.xml" qualifiers="land"><bool name="abc_action_bar_embed_tabs_pre_jb">true</bool><bool name="abc_config_allowActionMenuItemTextWithIcon">true</bool></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-land\dimens.xml" qualifiers="land"><dimen name="abc_action_bar_default_height_material">48.0dip</dimen><dimen name="abc_action_bar_default_padding_material">0.0dip</dimen><dimen name="abc_action_bar_progress_bar_size">32.0dip</dimen><dimen name="abc_text_size_subtitle_material_toolbar">12.0dip</dimen><dimen name="abc_text_size_title_material_toolbar">14.0dip</dimen></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-large\bools.xml" qualifiers="large-v4"><bool name="abc_action_bar_embed_tabs_pre_jb">true</bool><bool name="abc_config_allowActionMenuItemTextWithIcon">true</bool></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-large\dimens.xml" qualifiers="large-v4"><dimen name="abc_config_prefDialogWidth">440.0dip</dimen><dimen name="abc_search_view_text_min_width">192.0dip</dimen><item name="dialog_fixed_height_major" type="dimen">60.000004%</item><item name="dialog_fixed_height_minor" type="dimen">90.0%</item><item name="dialog_fixed_width_major" type="dimen">60.000004%</item><item name="dialog_fixed_width_minor" type="dimen">90.0%</item></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-large\integers.xml" qualifiers="large-v4"><integer name="abc_max_action_buttons">4</integer></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-large\styles.xml" qualifiers="large-v4"><style name="Base.Theme.AppCompat.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.Dialog.FixedSize"/><style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.Light.Dialog.FixedSize"/></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="domino_primary">#ff4caf50</color><color name="domino_primary_dark">#ff2e7d32</color><color name="domino_accent">#ffffab00</color><color name="domino_background">#ff121212</color><color name="domino_surface">#ff1e1e1e</color><color name="domino_text_primary">#ffffffff</color><color name="domino_text_secondary">#ffb3b3b3</color><color name="domino_button_positive">#ff66bb6a</color><color name="domino_button_negative">#ffef5350</color><color name="domino_button_neutral">#ff42a5f5</color><color name="domino_game_board">#ff2e2e2e</color><color name="domino_piece_white">#ffe0e0e0</color><color name="domino_piece_black">#ff616161</color><color name="domino_score_background">#ff1b5e20</color><color name="domino_player_highlight">#ff3e2723</color><color name="domino_connection_success">#ff4caf50</color><color name="domino_connection_error">#fff44336</color><color name="domino_waiting">#ffff9800</color></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-port\bools.xml" qualifiers="port"><bool name="abc_action_bar_embed_tabs">false</bool></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-sw600dp\dimens.xml" qualifiers="sw600dp-v13"><dimen name="abc_action_bar_content_inset_material">24.0dip</dimen><dimen name="abc_action_bar_default_height_material">64.0dip</dimen><dimen name="abc_action_bar_default_padding_material">4.0dip</dimen><dimen name="abc_action_bar_navigation_padding_start_material">8.0dip</dimen><dimen name="abc_action_bar_overflow_padding_end_material">18.0dip</dimen><dimen name="abc_config_prefDialogWidth">580.0dip</dimen><dimen name="abc_text_size_subtitle_material_toolbar">16.0dip</dimen><dimen name="abc_text_size_title_material_toolbar">20.0dip</dimen></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-sw600dp\integers.xml" qualifiers="sw600dp-v13"><integer name="abc_max_action_buttons">5</integer></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-v17\styles.xml" qualifiers="v17"><style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="@android:style/Widget">
        <item name="android:layout_gravity">start|center</item>
        <item name="android:paddingEnd">8.0dip</item>
    </style><style name="RtlOverlay.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton.Overflow">
        <item name="android:paddingStart">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="@android:style/Widget">
        <item name="android:paddingEnd">16.0dip</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="@android:style/Widget">
        <item name="android:layout_marginStart">16.0dip</item>
    </style><style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="@android:style/Widget">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:layout_alignParentStart">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="@android:style/Widget">
        <item name="android:paddingStart">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingEnd">4.0dip</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="@android:style/Widget">
        <item name="android:layout_alignParentStart">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="@android:style/Widget">
        <item name="android:layout_toStartOf">@id/edit_query</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="@android:style/Widget">
        <item name="android:layout_alignParentEnd">true</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="@style/Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toStartOf">@android:id/icon2</item>
        <item name="android:layout_toEndOf">@android:id/icon1</item>
    </style><style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="@android:style/Widget">
        <item name="android:layout_marginStart">@dimen/abc_dropdownitem_text_padding_left</item>
    </style><style name="RtlOverlay.Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/Base.Widget.AppCompat.Toolbar.Button.Navigation">
        <item name="android:paddingStart">@dimen/abc_action_bar_navigation_padding_start_material</item>
    </style></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-v18\dimens.xml" qualifiers="v18"><dimen name="abc_switch_padding">0.0px</dimen></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-v21\styles.xml" qualifiers="v21"><style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance.Material"/><style name="Base.TextAppearance.AppCompat.Body1" parent="@android:style/TextAppearance.Material.Body1"/><style name="Base.TextAppearance.AppCompat.Body2" parent="@android:style/TextAppearance.Material.Body2"/><style name="Base.TextAppearance.AppCompat.Button" parent="@android:style/TextAppearance.Material.Button"/><style name="Base.TextAppearance.AppCompat.Caption" parent="@android:style/TextAppearance.Material.Caption"/><style name="Base.TextAppearance.AppCompat.Display1" parent="@android:style/TextAppearance.Material.Display1"/><style name="Base.TextAppearance.AppCompat.Display2" parent="@android:style/TextAppearance.Material.Display2"/><style name="Base.TextAppearance.AppCompat.Display3" parent="@android:style/TextAppearance.Material.Display3"/><style name="Base.TextAppearance.AppCompat.Display4" parent="@android:style/TextAppearance.Material.Display4"/><style name="Base.TextAppearance.AppCompat.Headline" parent="@android:style/TextAppearance.Material.Headline"/><style name="Base.TextAppearance.AppCompat.Inverse" parent="@android:style/TextAppearance.Material.Inverse"/><style name="Base.TextAppearance.AppCompat.Large" parent="@android:style/TextAppearance.Material.Large"/><style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@android:style/TextAppearance.Material.Large.Inverse"/><style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large"/><style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small"/><style name="Base.TextAppearance.AppCompat.Medium" parent="@android:style/TextAppearance.Material.Medium"/><style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@android:style/TextAppearance.Material.Medium.Inverse"/><style name="Base.TextAppearance.AppCompat.Menu" parent="@android:style/TextAppearance.Material.Menu"/><style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@android:style/TextAppearance.Material.SearchResult.Subtitle"/><style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@android:style/TextAppearance.Material.SearchResult.Title"/><style name="Base.TextAppearance.AppCompat.Small" parent="@android:style/TextAppearance.Material.Small"/><style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@android:style/TextAppearance.Material.Small.Inverse"/><style name="Base.TextAppearance.AppCompat.Subhead" parent="@android:style/TextAppearance.Material.Subhead"/><style name="Base.TextAppearance.AppCompat.Title" parent="@android:style/TextAppearance.Material.Title"/><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Menu"/><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle"/><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle.Inverse"/><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title"/><style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title.Inverse"/><style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Subtitle"/><style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Title"/><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large"/><style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small"/><style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@android:style/TextAppearance.Material.Button"/><style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@android:style/TextAppearance.Material.Widget.TextView.SpinnerItem"/><style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle"/><style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title"/><style name="Base.Theme.AppCompat" parent="@style/Base.V21.Theme.AppCompat"/><style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V21.Theme.AppCompat.Dialog"/><style name="Base.Theme.AppCompat.Light" parent="@style/Base.V21.Theme.AppCompat.Light"/><style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V21.Theme.AppCompat.Light.Dialog"/><style name="Base.Widget.AppCompat.ActionBar.TabText" parent="@android:style/Widget.Material.ActionBar.TabText"/><style name="Base.Widget.AppCompat.ActionBar.TabView" parent="@android:style/Widget.Material.ActionBar.TabView"/><style name="Base.Widget.AppCompat.ActionButton" parent="@android:style/Widget.Material.ActionButton"/><style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@android:style/Widget.Material.ActionButton.CloseMode">
        <item name="android:minWidth">56.0dip</item>
    </style><style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@android:style/Widget.Material.ActionButton.Overflow"/><style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.Material.AutoCompleteTextView"/><style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget.Material.Button"/><style name="Base.Widget.AppCompat.Button.Borderless" parent="@android:style/Widget.Material.Button.Borderless"/><style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@android:style/Widget.Material.Button.Borderless.Colored"/><style name="Base.Widget.AppCompat.Button.Small" parent="@android:style/Widget.Material.Button.Small"/><style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget.Material.ButtonBar"/><style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.Material.CompoundButton.CheckBox"/><style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.Material.CompoundButton.RadioButton"/><style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="@android:style/Widget.Material.DropDownItem.Spinner"/><style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@android:style/Widget.Material.Light.ActionBar.TabText"/><style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@android:style/Widget.Material.Light.ActionBar.TabText"/><style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@android:style/Widget.Material.Light.ActionBar.TabView"/><style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@android:style/Widget.Material.Light.PopupMenu"/><style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="android:overlapAnchor">true</item>
    </style><style name="Base.Widget.AppCompat.ListPopupWindow" parent="@android:style/Widget.Material.ListPopupWindow"/><style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.Material.ListView"/><style name="Base.Widget.AppCompat.ListView.DropDown" parent="@android:style/Widget.Material.ListView.DropDown"/><style name="Base.Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView"/><style name="Base.Widget.AppCompat.PopupMenu" parent="@android:style/Widget.Material.PopupMenu"/><style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="android:overlapAnchor">true</item>
    </style><style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Material.ProgressBar"/><style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Material.ProgressBar.Horizontal"/><style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.Material.RatingBar"/><style name="Base.Widget.AppCompat.Spinner" parent="@android:style/Widget.Material.Spinner"/><style name="Base.Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@android:style/Widget.Material.Spinner">
        <item name="disableChildrenWhenDisabled">true</item>
        <item name="popupPromptView">@layout/abc_simple_dropdown_hint</item>
        <item name="spinnerMode">dropdown</item>
    </style><style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@android:style/Widget.Material.Spinner.Underlined"/><style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.Material.TextView.SpinnerItem"/><style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget.Material.Toolbar.Button.Navigation"/><style name="Platform.AppCompat" parent="@android:style/Theme.Material">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
    </style><style name="Platform.AppCompat.Light" parent="@android:style/Theme.Material.Light">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
    </style><style name="Platform.ThemeOverlay.AppCompat.Dark" parent="">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
    </style><style name="Platform.ThemeOverlay.AppCompat.Light" parent="">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
    </style><style name="Base.V21.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
        <item name="actionBarDivider">?android:actionBarDivider</item>
        <item name="actionBarItemBackground">?android:actionBarItemBackground</item>
        <item name="actionBarSize">?android:actionBarSize</item>
        <item name="actionButtonStyle">?android:actionButtonStyle</item>
        <item name="actionMenuTextAppearance">?android:actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:actionMenuTextColor</item>
        <item name="actionModeBackground">?android:actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:actionModeCloseDrawable</item>
        <item name="actionModeShareDrawable">?android:actionModeShareDrawable</item>
        <item name="actionOverflowButtonStyle">?android:actionOverflowButtonStyle</item>
        <item name="autoCompleteTextViewStyle">?android:autoCompleteTextViewStyle</item>
        <item name="buttonStyle">?android:buttonStyle</item>
        <item name="buttonStyleSmall">?android:buttonStyleSmall</item>
        <item name="checkboxStyle">?android:checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:dividerHorizontal</item>
        <item name="dividerVertical">?android:dividerVertical</item>
        <item name="editTextBackground">?android:editTextBackground</item>
        <item name="editTextColor">?android:editTextColor</item>
        <item name="editTextStyle">?android:editTextStyle</item>
        <item name="homeAsUpIndicator">?android:homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:radioButtonStyle</item>
        <item name="ratingBarStyle">?android:ratingBarStyle</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:textAppearanceSmallPopupMenu</item>
    </style><style name="Base.V21.Theme.AppCompat.Dialog" parent="@style/Base.V11.Theme.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style><style name="Base.V21.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
        <item name="actionBarDivider">?android:actionBarDivider</item>
        <item name="actionBarItemBackground">?android:actionBarItemBackground</item>
        <item name="actionBarSize">?android:actionBarSize</item>
        <item name="actionButtonStyle">?android:actionButtonStyle</item>
        <item name="actionMenuTextAppearance">?android:actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:actionMenuTextColor</item>
        <item name="actionModeBackground">?android:actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:actionModeCloseDrawable</item>
        <item name="actionModeShareDrawable">?android:actionModeShareDrawable</item>
        <item name="actionOverflowButtonStyle">?android:actionOverflowButtonStyle</item>
        <item name="autoCompleteTextViewStyle">?android:autoCompleteTextViewStyle</item>
        <item name="buttonStyle">?android:buttonStyle</item>
        <item name="buttonStyleSmall">?android:buttonStyleSmall</item>
        <item name="checkboxStyle">?android:checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:dividerHorizontal</item>
        <item name="dividerVertical">?android:dividerVertical</item>
        <item name="editTextBackground">?android:editTextBackground</item>
        <item name="editTextColor">?android:editTextColor</item>
        <item name="editTextStyle">?android:editTextStyle</item>
        <item name="homeAsUpIndicator">?android:homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:radioButtonStyle</item>
        <item name="ratingBarStyle">?android:ratingBarStyle</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:textAppearanceSmallPopupMenu</item>
    </style><style name="Base.V21.Theme.AppCompat.Light.Dialog" parent="@style/Base.V11.Theme.AppCompat.Light.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-w360dp\integers.xml" qualifiers="w360dp-v13"><integer name="abc_max_action_buttons">3</integer></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-w480dp\bools.xml" qualifiers="w480dp-v13"><bool name="abc_action_bar_embed_tabs_pre_jb">true</bool><bool name="abc_config_allowActionMenuItemTextWithIcon">true</bool></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-w500dp\integers.xml" qualifiers="w500dp-v13"><integer name="abc_max_action_buttons">4</integer></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-w600dp\dimens.xml" qualifiers="w600dp-v13"><dimen name="abc_search_view_text_min_width">192.0dip</dimen></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-w600dp\integers.xml" qualifiers="w600dp-v13"><integer name="abc_max_action_buttons">5</integer></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-w720dp\bools.xml" qualifiers="w720dp-v13"><bool name="abc_action_bar_expanded_action_views_exclusive">false</bool></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-w820dp\dimens.xml" qualifiers="w820dp-v13"><dimen name="activity_horizontal_margin">64.0dip</dimen></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-xlarge\bools.xml" qualifiers="xlarge-v4"><bool name="abc_action_bar_expanded_action_views_exclusive">false</bool></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-xlarge\dimens.xml" qualifiers="xlarge-v4"><item name="dialog_fixed_width_major" type="dimen">50.0%</item><item name="dialog_fixed_width_minor" type="dimen">70.00001%</item></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-xlarge\integers.xml" qualifiers="xlarge-v4"><integer name="abc_max_action_buttons">5</integer></file><file path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\values-xlarge-land\dimens.xml" qualifiers="xlarge-land-v4"><dimen name="abc_search_view_text_min_width">256.0dip</dimen></file><file name="splits0" path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\main\res\xml\splits0.xml" qualifiers="" type="xml"/></source><source path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\build\generated\res\rs\debug"/><source path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Desktop\0000\5\Domino_v0.4(3)_base_src\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>