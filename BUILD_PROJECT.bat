@echo off
echo ========================================
echo    بناء مشروع لعبة الدومينو
echo    Domino Game Build Script
echo ========================================
echo.

REM إنشاء مجلد جديد بمسار صحيح
echo [1/5] إنشاء مجلد المشروع الجديد...
if not exist "C:\DominoGame" (
    mkdir "C:\DominoGame"
    echo ✅ تم إنشاء المجلد: C:\DominoGame
) else (
    echo ⚠️  المجلد موجود مسبقاً: C:\DominoGame
)

echo.
echo [2/5] نسخ ملفات البناء...

REM نسخ ملفات البناء الأساسية
copy "build.gradle" "C:\DominoGame\" >nul 2>&1
copy "settings.gradle" "C:\DominoGame\" >nul 2>&1
copy "gradle.properties" "C:\DominoGame\" >nul 2>&1
copy "local.properties" "C:\DominoGame\" >nul 2>&1
copy "gradlew.bat" "C:\DominoGame\" >nul 2>&1

REM نسخ مجلد gradle
if exist "gradle" (
    xcopy /E /I /Q "gradle" "C:\DominoGame\gradle" >nul 2>&1
)

echo ✅ تم نسخ ملفات البناء

echo.
echo [3/5] نسخ مجلد التطبيق...

REM نسخ مجلد app بالكامل
if exist "app" (
    xcopy /E /I /Q "app" "C:\DominoGame\app" >nul 2>&1
    echo ✅ تم نسخ مجلد app
) else (
    echo ❌ مجلد app غير موجود!
    pause
    exit /b 1
)

echo.
echo [4/5] التحقق من متطلبات البناء...

REM التحقق من Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Java 8 أو أحدث
    pause
    exit /b 1
) else (
    echo ✅ Java متوفر
)

REM التحقق من Gradle
gradle -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Gradle غير متوفر، سيتم استخدام Gradle Wrapper
) else (
    echo ✅ Gradle متوفر
)

echo.
echo [5/5] بناء المشروع...
echo يرجى الانتظار، قد تستغرق هذه العملية عدة دقائق...

REM الانتقال إلى مجلد المشروع الجديد
cd /d "C:\DominoGame"

REM محاولة البناء باستخدام Gradle
gradle clean assembleDebug
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  فشل البناء باستخدام Gradle، محاولة استخدام Gradle Wrapper...
    gradlew.bat clean assembleDebug
    if %errorlevel% neq 0 (
        echo.
        echo ❌ فشل في بناء المشروع!
        echo يرجى التحقق من:
        echo - تثبيت Android SDK
        echo - إعدادات local.properties
        echo - اتصال الإنترنت
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo 🎉 تم بناء المشروع بنجاح!
echo ========================================
echo.
echo 📁 مكان ملف APK:
echo C:\DominoGame\app\build\outputs\apk\debug\app-debug.apk
echo.
echo 📱 لتثبيت اللعبة على الجهاز:
echo adb install app\build\outputs\apk\debug\app-debug.apk
echo.
echo 🎮 الميزات المتوفرة:
echo ✅ لعبة دومينو كوبية كاملة
echo ✅ دعم اللغة العربية
echo ✅ ذكاء اصطناعي بثلاث مستويات
echo ✅ واجهة مستخدم حديثة
echo ✅ دعم البلوتوث للعب الجماعي
echo.
echo استمتع باللعب! 🎲
echo.
pause
