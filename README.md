# Domino Game - Cuban Style 🎮

A complete Android domino game implementation with Cuban-style rules, featuring single-player AI opponents and multiplayer Bluetooth connectivity.

## 🌟 Features

### Game Features
- **Cuban-style domino rules** - Authentic gameplay experience
- **Multiple AI difficulty levels** - Easy, Medium, and Hard AI opponents
- **Single-player mode** - Play against 3 AI opponents
- **Multiplayer mode** - Play with friends via Bluetooth
- **Demo mode** - Watch AI vs AI games
- **Team play support** - 2v2 team games
- **Multiple domino sets** - Standard 28-piece and Double-nine 55-piece sets
- **Customizable scoring** - Adjustable maximum score (50-500 points)

### Technical Features
- **Modern Android UI** - Material Design with custom themes
- **Arabic language support** - Complete RTL localization
- **Dark mode support** - Automatic theme switching
- **Bluetooth connectivity** - Peer-to-peer multiplayer gaming
- **Advanced AI** - Strategic gameplay with blocking and optimization
- **Smooth animations** - Enhanced user experience
- **Debug tools** - Built-in debugging and testing features

## 🎯 Game Rules (Cuban Style)

1. **Setup**: Each player gets 7 pieces (4 players) or 10 pieces (2 players)
2. **Starting**: Player with highest double starts the game
3. **Gameplay**: Players take turns placing pieces that match the ends of the chain
4. **Passing**: If a player cannot play, they must pass
5. **Winning**: First player to play all pieces wins the round
6. **Scoring**: Winner gets points equal to sum of all opponents' remaining pieces
7. **Game End**: First player to reach the target score wins

## 🏗️ Project Structure

```
src/main/java/ratmil/domino/
├── model/                  # Game logic and data models
│   ├── DominoPiece.java   # Domino piece representation
│   ├── Player.java        # Player management
│   ├── GameBoard.java     # Game board logic
│   └── DominoGame.java    # Main game controller
├── ai/                    # Artificial Intelligence
│   └── DominoAI.java      # AI strategy implementation
├── Activities/            # Android UI activities
│   ├── DominoMainActivity.java      # Main menu
│   ├── DominoActivity.java          # Game screen
│   ├── DominoGameSetupActivity.java # Game configuration
│   ├── DominoSettingsActivity.java  # Settings
│   ├── DominoConnectionActivity.java # Bluetooth setup
│   └── DebugActivity.java           # Debug tools
└── GameConnectionService.java       # Bluetooth service

res/
├── layout/               # UI layouts
│   ├── domino_main_menu.xml
│   ├── domino_game_board.xml
│   ├── domino_settings.xml
│   └── domino_piece_item.xml
├── values/              # Resources
│   ├── strings.xml      # English strings
│   ├── colors.xml       # Color definitions
│   └── styles.xml       # UI styles
├── values-ar/           # Arabic localization
│   └── strings.xml
├── values-night/        # Dark theme
│   └── colors.xml
├── drawable/            # Custom graphics
├── anim/               # Animations
└── menu/               # Menu definitions
```

## 🚀 Getting Started

### Prerequisites
- Android Studio 4.0+
- Android SDK API 21+ (Android 5.0)
- Bluetooth-enabled Android device for multiplayer

### Installation
1. Clone or download the project
2. Open in Android Studio
3. Build and run on your Android device

### Building APK
```bash
./gradlew assembleDebug
```

## 🎮 How to Play

### Single Player
1. Launch the app
2. Tap "New Game"
3. Configure game settings
4. Select AI difficulty
5. Start playing!

### Multiplayer (Bluetooth)
1. Ensure Bluetooth is enabled on both devices
2. One player taps "Create Game"
3. Other player taps "Join Game"
4. Select the host device from the list
5. Wait for connection and start playing!

### Demo Mode
1. Tap "Demo Mode" from main menu
2. Watch AI players compete
3. Learn strategies and game flow

## 🔧 Configuration

### Game Settings
- **Max Score**: 50, 100, 150, 200, 300, or 500 points
- **Domino Set**: 28-piece (standard) or 55-piece (double-nine)
- **Team Play**: Enable 2v2 team games
- **AI Difficulty**: Easy, Medium, Hard, or Mixed
- **Speed**: Slow, Normal, or Fast AI moves
- **Theme**: Multiple color themes + dark mode

### AI Difficulty Levels
- **Easy**: Random moves, good for beginners
- **Medium**: Basic strategy, plays doubles first
- **Hard**: Advanced strategy with blocking and optimization
- **Mixed**: Combination of all difficulty levels

## 🧠 AI Strategy

The AI implements sophisticated domino strategies:

### Easy AI
- Random piece selection
- No strategic planning

### Medium AI
- Prioritizes playing doubles
- Plays highest value pieces first
- Basic hand management

### Hard AI
- **Blocking strategy**: Prevents opponents from playing
- **Hand optimization**: Maximizes future play options
- **Endgame planning**: Strategic piece retention
- **Opponent analysis**: Tracks opponent capabilities

## 🌐 Localization

Currently supported languages:
- **English** (default)
- **Arabic** (العربية) - Complete RTL support

To add new languages:
1. Create `res/values-[language]/strings.xml`
2. Translate all string resources
3. Test RTL layout if applicable

## 🎨 Theming

### Available Themes
- Green (default)
- Blue
- Red
- Yellow
- Dark mode (automatic)

### Custom Colors
The app uses a comprehensive color system:
- Primary colors for main UI elements
- Accent colors for highlights
- Game-specific colors for board and pieces
- Semantic colors for success/error states

## 🔧 Technical Details

### Architecture
- **Model-View-Controller** pattern
- **Service-oriented** Bluetooth communication
- **Observer pattern** for game state updates
- **Strategy pattern** for AI implementations

### Key Classes
- `DominoGame`: Main game controller and state management
- `DominoAI`: AI strategy implementation with multiple difficulty levels
- `GameBoard`: Board state and piece placement logic
- `GameConnectionService`: Bluetooth communication service

### Performance
- Efficient game state management
- Optimized AI algorithms
- Smooth animations with minimal overhead
- Memory-conscious design

## 🐛 Debug Features

Access debug mode through Settings → Debug to view:
- System information
- Memory usage
- Game settings
- Logic testing
- Connection status

## 📱 Compatibility

- **Minimum SDK**: API 21 (Android 5.0)
- **Target SDK**: API 26 (Android 8.0)
- **Bluetooth**: Required for multiplayer
- **Screen sizes**: Phones and tablets
- **Orientations**: Portrait and landscape

## 🤝 Contributing

This is a complete, working domino game implementation. Feel free to:
- Add new AI strategies
- Implement additional game variants
- Improve UI/UX
- Add new languages
- Optimize performance

## 📄 License

This project is provided as-is for educational and entertainment purposes.

## 🎯 Future Enhancements

Potential improvements:
- Online multiplayer (WiFi/Internet)
- Tournament mode
- Statistics tracking
- Achievement system
- Sound effects and music
- Improved graphics and animations
- Additional domino variants (Block, All-Fives, etc.)

---

**Enjoy playing Domino! 🎲**
