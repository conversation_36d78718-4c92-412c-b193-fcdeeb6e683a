{"logs": [{"outputFile": "ratmil.domino.app-mergeDebugResources-27:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d36bbf6aff7e8e44ac7f7d3eb2fff2ee\\transformed\\core-1.9.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,335,336,337,338,540,543", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,27788,27904,28030,28156,40152,40324", "endLines": "2,17,18,19,335,336,337,338,542,547", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,27899,28025,28151,28279,40319,40671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8679f45878a6127de1b97684ca2efa9a\\transformed\\material-1.8.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,25,28,31,34,37,40,43,46,49,52,55,56,59,64,75,81,91,101,111,121,131,141,151,161,171,181,191,201,211,221,231,237,243,249,255,259,263,264,265,266,270,273,276,279,282,283,286,289,293,297", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1383,1490,1595,1714,1839,1960,2173,2432,2703,2921,3153,3389,3639,3852,4061,4292,4493,4609,4779,5100,6129,6586,7137,7692,8248,8809,9361,9912,10464,11017,11566,12119,12675,13230,13776,14330,14885,15177,15471,15771,16071,16400,16741,16879,17023,17179,17572,17790,18012,18238,18454,18564,18734,18924,19165,19424", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,27,30,33,36,39,42,45,48,51,54,55,58,63,74,80,90,100,110,120,130,140,150,160,170,180,190,200,210,220,230,236,242,248,254,258,262,263,264,265,269,272,275,278,281,282,285,288,292,296,299", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1378,1485,1590,1709,1834,1955,2168,2427,2698,2916,3148,3384,3634,3847,4056,4287,4488,4604,4774,5095,6124,6581,7132,7687,8243,8804,9356,9907,10459,11012,11561,12114,12670,13225,13771,14325,14880,15172,15466,15766,16066,16395,16736,16874,17018,17174,17567,17785,18007,18233,18449,18559,18729,18919,19160,19419,19596"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,67,68,69,70,72,73,74,77,80,167,170,173,176,182,185,188,242,245,246,249,254,265,339,349,359,369,379,389,399,409,419,429,439,449,459,469,479,489,495,501,507,513,517,521,522,523,524,528,531,534,537,548,549,552,555,559,563", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,7224,7317,7424,7529,7751,7876,7997,8210,8469,15070,15288,15520,15756,16205,16418,16627,21732,21933,22049,22219,22540,23569,28284,28835,29390,29946,30507,31059,31610,32162,32715,33264,33817,34373,34928,35474,36028,36583,36875,37169,37469,37769,38098,38439,38577,38721,38877,39270,39488,39710,39936,40676,40786,40956,41146,41387,41646", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,67,68,69,70,72,73,76,79,82,169,172,175,178,184,187,190,244,245,248,253,264,270,348,358,368,378,388,398,408,418,428,438,448,458,468,478,488,494,500,506,512,516,520,521,522,523,527,530,533,536,539,548,551,554,558,562,565", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,7312,7419,7524,7643,7871,7992,8205,8464,8735,15283,15515,15751,16001,16413,16622,16853,21928,22044,22214,22535,23564,24021,28830,29385,29941,30502,31054,31605,32157,32710,33259,33812,34368,34923,35469,36023,36578,36870,37164,37464,37764,38093,38434,38572,38716,38872,39265,39483,39705,39931,40147,40781,40951,41141,41382,41641,41818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9ae72c11e6e08c08a7e177bedd18d43\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "42,43,62,155,184,187,216,218,224,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3743,3862,5422,11435,13630,13807,15659,15841,16344,17082,17706", "endLines": "42,47,62,157,186,187,216,218,233,247,259", "endColumns": "118,12,102,12,12,98,90,92,12,12,12", "endOffsets": "3857,4208,5520,11629,13802,13901,15745,15929,16962,17701,18337"}, "to": {"startLines": "51,52,71,179,207,210,231,239,283,311,323", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5523,5642,7648,16006,18593,18770,20756,21381,24736,26528,27152", "endLines": "51,56,71,181,209,210,231,239,292,322,334", "endColumns": "118,12,102,12,12,98,90,92,12,12,12", "endOffsets": "5637,5988,7746,16200,18765,18864,20842,21469,25354,27147,27783"}}, {"source": "D:\\Desktop\\0000\\5\\Domino_v0.4(3)_base_src\\app\\src\\main\\res\\values-v21\\styles.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,118,157,160,199,43,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,67,68,69,70,71,72,76,77,78,79,80,85,86,87,88,94,100,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,154,265,376,489,604,721,838,955,1072,1189,1304,1415,1542,1693,1844,1957,2086,2195,2338,2475,2586,2713,2828,2939,3082,3233,3400,3545,3706,3859,4006,4151,4296,4416,4571,4720,4863,4946,5043,5138,11569,14529,14722,17694,5247,5366,5485,5594,5787,5914,6039,6136,6255,6390,6499,6602,6733,6870,6995,7126,7265,7396,7511,7768,7883,7984,8103,8207,8310,8555,8662,8791,8894,8993,9309,9430,9555,9690,10039,10400,10984", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,156,159,198,201,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,66,67,68,69,70,71,75,76,77,78,79,84,85,86,87,93,99,108,117", "endColumns": "98,110,110,112,114,116,116,116,116,116,114,110,126,150,150,112,128,108,142,136,110,126,114,110,142,150,166,144,160,152,146,144,144,119,154,148,142,82,96,94,108,12,12,12,12,118,118,108,12,126,124,96,118,134,108,102,130,136,124,130,138,130,114,12,114,100,118,103,102,12,106,128,102,98,12,120,124,134,12,12,12,12", "endOffsets": "149,260,371,484,599,716,833,950,1067,1184,1299,1410,1537,1688,1839,1952,2081,2190,2333,2470,2581,2708,2823,2934,3077,3228,3395,3540,3701,3854,4001,4146,4291,4411,4566,4715,4858,4941,5038,5133,5242,14524,14717,17689,17894,5361,5480,5589,5782,5909,6034,6131,6250,6385,6494,6597,6728,6865,6990,7121,7260,7391,7506,7763,7878,7979,8098,8202,8305,8550,8657,8786,8889,8988,9304,9425,9550,9685,10034,10395,10979,11564"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,57,58,59,60,61,62,63,64,65,66,83,122,125,164,191,192,193,194,197,198,199,200,201,202,203,204,205,206,211,212,213,214,215,219,220,221,222,223,224,228,229,230,232,233,238,240,241,271,277,293,302", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1603,1701,1811,1921,2033,2147,2263,2379,2495,2611,2727,2841,2951,3077,3227,3377,3489,3617,3725,3867,4003,4113,4239,4353,4463,4605,4755,4921,5065,5225,5377,5993,6137,6281,6400,6554,6702,6844,6926,7022,7116,8740,11700,11893,14865,16858,16976,17094,17202,17395,17521,17645,17741,17859,17993,18101,18203,18333,18469,18869,18999,19137,19267,19381,19638,19752,19852,19970,20073,20175,20420,20526,20654,20847,20945,21261,21474,21598,24026,24375,25359,25943", "endLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,57,58,59,60,61,62,63,64,65,66,121,124,163,166,191,192,193,196,197,198,199,200,201,202,203,204,205,206,211,212,213,214,218,219,220,221,222,223,227,228,229,230,232,237,238,240,241,276,282,301,310", "endColumns": "97,109,109,111,113,115,115,115,115,115,113,109,125,149,149,111,127,107,141,135,109,125,113,109,141,149,165,143,159,151,145,143,143,118,153,147,141,81,95,93,107,12,12,12,12,117,117,107,12,125,123,95,117,133,107,101,129,135,123,129,137,129,113,12,113,99,117,102,101,12,105,127,101,97,12,119,123,133,12,12,12,12", "endOffsets": "1696,1806,1916,2028,2142,2258,2374,2490,2606,2722,2836,2946,3072,3222,3372,3484,3612,3720,3862,3998,4108,4234,4348,4458,4600,4750,4916,5060,5220,5372,5518,6132,6276,6395,6549,6697,6839,6921,7017,7111,7219,11695,11888,14860,15065,16971,17089,17197,17390,17516,17640,17736,17854,17988,18096,18198,18328,18464,18588,18994,19132,19262,19376,19633,19747,19847,19965,20068,20170,20415,20521,20649,20751,20940,21256,21376,21593,21727,24370,24731,25938,26523"}}]}]}