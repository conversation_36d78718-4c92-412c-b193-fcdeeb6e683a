{"logs": [{"outputFile": "ratmil.domino.app-mergeDebugResources-27:/values-sw600dp-v13/values-sw600dp-v13.xml", "map": [{"source": "D:\\Desktop\\0000\\5\\Domino_v0.4(3)_base_src\\app\\src\\main\\res\\values-sw600dp\\dimens.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,127,200,273,355,434,496,570", "endColumns": "71,72,72,81,78,61,73,70", "endOffsets": "122,195,268,350,429,491,565,636"}, "to": {"startLines": "2,4,6,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,196,343,492,574,653,715,789", "endColumns": "71,72,72,81,78,61,73,70", "endOffsets": "122,264,411,569,648,710,784,855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8679f45878a6127de1b97684ca2efa9a\\transformed\\material-1.8.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,185,256,328,386,444,553,617,673,732,795", "endLines": "2,3,4,5,6,7,9,10,11,12,13,17", "endColumns": "59,69,70,71,57,57,10,63,55,58,62,10", "endOffsets": "110,180,251,323,381,439,548,612,668,727,790,962"}, "to": {"startLines": "13,14,15,16,17,18,19,21,22,23,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "860,920,990,1061,1133,1191,1249,1358,1422,1478,1592,1655", "endLines": "13,14,15,16,17,18,20,21,22,23,25,29", "endColumns": "59,69,70,71,57,57,10,63,55,58,62,10", "endOffsets": "915,985,1056,1128,1186,1244,1353,1417,1473,1532,1650,1822"}}, {"source": "D:\\Desktop\\0000\\5\\Domino_v0.4(3)_base_src\\app\\src\\main\\res\\values-sw600dp\\integers.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "54", "endOffsets": "105"}, "to": {"startLines": "24", "startColumns": "4", "startOffsets": "1537", "endColumns": "54", "endOffsets": "1587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9ae72c11e6e08c08a7e177bedd18d43\\transformed\\appcompat-1.6.1\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "3,5,6", "startColumns": "4,4,4", "startOffsets": "124,263,337", "endColumns": "68,73,75", "endOffsets": "188,332,408"}, "to": {"startLines": "3,5,7", "startColumns": "4,4,4", "startOffsets": "127,269,416", "endColumns": "68,73,75", "endOffsets": "191,338,487"}}]}]}