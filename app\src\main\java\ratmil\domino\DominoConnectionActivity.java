package ratmil.domino;

import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.Set;

/**
 * Activity for Bluetooth connection management
 */
public class DominoConnectionActivity extends Activity {
    
    private static final int REQUEST_ENABLE_BT = 1;
    private static final int REQUEST_DISCOVERABLE = 2;
    
    private BluetoothAdapter bluetoothAdapter;
    private ArrayAdapter<String> deviceListAdapter;
    private ArrayList<BluetoothDevice> discoveredDevices;
    
    private TextView statusText;
    private ListView deviceListView;
    private Button scanButton;
    private Button makeDiscoverableButton;
    private Button cancelButton;
    
    private String connectionType; // "join" or "create"
    private boolean isScanning = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.domino_connection);
        
        connectionType = getIntent().getStringExtra("connection_type");
        
        initializeBluetooth();
        initializeViews();
        setupClickListeners();
        
        if (bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
            setupDeviceList();
        }
    }
    
    private void initializeBluetooth() {
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        
        if (bluetoothAdapter == null) {
            Toast.makeText(this, R.string.error_bluetooth_not_supported, Toast.LENGTH_LONG).show();
            finish();
            return;
        }
        
        if (!bluetoothAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
        }
    }
    
    private void initializeViews() {
        statusText = findViewById(R.id.status_text);
        deviceListView = findViewById(R.id.device_list);
        scanButton = findViewById(R.id.scan_button);
        makeDiscoverableButton = findViewById(R.id.make_discoverable_button);
        cancelButton = findViewById(R.id.cancel_button);
        
        discoveredDevices = new ArrayList<>();
        deviceListAdapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_1);
        deviceListView.setAdapter(deviceListAdapter);
        
        updateStatusText();
    }
    
    private void setupClickListeners() {
        scanButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isScanning) {
                    stopScanning();
                } else {
                    startScanning();
                }
            }
        });
        
        makeDiscoverableButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                makeDiscoverable();
            }
        });
        
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        
        deviceListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                connectToDevice(position);
            }
        });
    }
    
    private void setupDeviceList() {
        // Add paired devices
        Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
        
        if (pairedDevices.size() > 0) {
            for (BluetoothDevice device : pairedDevices) {
                deviceListAdapter.add(device.getName() + "\n" + device.getAddress() + " (Paired)");
                discoveredDevices.add(device);
            }
        }
        
        // Register for broadcasts when a device is discovered
        IntentFilter filter = new IntentFilter(BluetoothDevice.ACTION_FOUND);
        registerReceiver(deviceFoundReceiver, filter);
        
        // Register for broadcasts when discovery has finished
        filter = new IntentFilter(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        registerReceiver(discoveryFinishedReceiver, filter);
    }
    
    private void startScanning() {
        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            Toast.makeText(this, "Bluetooth not available", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Clear previous results
        deviceListAdapter.clear();
        discoveredDevices.clear();
        
        // Add paired devices first
        setupDeviceList();
        
        // Start discovery
        if (bluetoothAdapter.startDiscovery()) {
            isScanning = true;
            scanButton.setText(R.string.scanning);
            statusText.setText(R.string.scanning);
        }
    }
    
    private void stopScanning() {
        if (bluetoothAdapter != null && bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
        isScanning = false;
        scanButton.setText("Scan for Devices");
        updateStatusText();
    }
    
    private void makeDiscoverable() {
        Intent discoverableIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_DISCOVERABLE);
        discoverableIntent.putExtra(BluetoothAdapter.EXTRA_DISCOVERABLE_DURATION, 300);
        startActivityForResult(discoverableIntent, REQUEST_DISCOVERABLE);
    }
    
    private void connectToDevice(int position) {
        if (position < discoveredDevices.size()) {
            BluetoothDevice device = discoveredDevices.get(position);
            
            // Stop scanning
            stopScanning();
            
            // Start connection process
            Toast.makeText(this, "Connecting to " + device.getName(), Toast.LENGTH_SHORT).show();
            
            // Start game connection service
            Intent serviceIntent = new Intent(this, GameConnectionService.class);
            serviceIntent.putExtra("device", device);
            serviceIntent.putExtra("connection_type", connectionType);
            startService(serviceIntent);
            
            // Go to waiting screen
            Intent intent = new Intent(this, DominoWaitingActivity.class);
            intent.putExtra("device_name", device.getName());
            intent.putExtra("connection_type", connectionType);
            startActivity(intent);
            finish();
        }
    }
    
    private void updateStatusText() {
        if ("create".equals(connectionType)) {
            statusText.setText("Creating game - Make device discoverable and wait for players to join");
            makeDiscoverableButton.setVisibility(View.VISIBLE);
        } else {
            statusText.setText("Joining game - Select a device to connect to");
            makeDiscoverableButton.setVisibility(View.GONE);
        }
    }
    
    // BroadcastReceiver for discovering devices
    private final BroadcastReceiver deviceFoundReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            
            if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                
                if (device != null && !discoveredDevices.contains(device)) {
                    String deviceInfo = device.getName() + "\n" + device.getAddress();
                    if (device.getBondState() != BluetoothDevice.BOND_BONDED) {
                        deviceInfo += " (Available)";
                    }
                    
                    deviceListAdapter.add(deviceInfo);
                    discoveredDevices.add(device);
                }
            }
        }
    };
    
    // BroadcastReceiver for when discovery is finished
    private final BroadcastReceiver discoveryFinishedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            isScanning = false;
            scanButton.setText("Scan for Devices");
            
            if (deviceListAdapter.getCount() == 0) {
                statusText.setText(R.string.no_devices);
            } else {
                updateStatusText();
            }
        }
    };
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                if (resultCode == RESULT_OK) {
                    setupDeviceList();
                } else {
                    Toast.makeText(this, "Bluetooth is required for multiplayer", Toast.LENGTH_LONG).show();
                    finish();
                }
                break;
            case REQUEST_DISCOVERABLE:
                if (resultCode > 0) {
                    Toast.makeText(this, "Device is discoverable for " + resultCode + " seconds", 
                        Toast.LENGTH_LONG).show();
                }
                break;
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // Stop discovery
        if (bluetoothAdapter != null && bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
        
        // Unregister broadcast receivers
        try {
            unregisterReceiver(deviceFoundReceiver);
            unregisterReceiver(discoveryFinishedReceiver);
        } catch (IllegalArgumentException e) {
            // Receivers not registered
        }
    }
}
