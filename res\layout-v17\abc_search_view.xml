<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:orientation="horizontal"
    android:id="@id/search_bar"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView
        android:textAppearance="?android:textAppearanceMedium"
        android:textColor="?android:textColorPrimary"
        android:gravity="center_vertical"
        android:id="@id/search_badge"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="fill_parent"
        android:layout_marginBottom="2.0dip"
        android:drawablePadding="0.0dip" />
    <android.support.v7.internal.widget.TintImageView
        android:layout_gravity="center_vertical"
        android:id="@id/search_button"
        android:focusable="true"
        android:layout_width="wrap_content"
        android:layout_height="fill_parent"
        android:contentDescription="@string/abc_searchview_description_search"
        style="?actionButtonStyle" />
    <LinearLayout
        android:layout_gravity="center_vertical"
        android:orientation="horizontal"
        android:id="@id/search_edit_frame"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8.0dip"
        android:layout_marginTop="4.0dip"
        android:layout_marginRight="8.0dip"
        android:layout_marginBottom="4.0dip"
        android:layout_weight="1.0"
        android:layoutDirection="locale">
        <android.support.v7.internal.widget.TintImageView
            android:layout_gravity="center_vertical"
            android:id="@id/search_mag_icon"
            android:visibility="gone"
            android:layout_width="@dimen/abc_dropdownitem_icon_width"
            android:layout_height="wrap_content"
            android:scaleType="centerInside"
            style="@style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon" />
        <LinearLayout
            android:layout_gravity="center_vertical"
            android:orientation="horizontal"
            android:id="@id/search_plate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1.0">
            <view
                android:ellipsize="end"
                android:layout_gravity="bottom"
                android:id="@id/search_src_text"
                android:background="@null"
                android:paddingLeft="@dimen/abc_dropdownitem_text_padding_left"
                android:paddingRight="@dimen/abc_dropdownitem_text_padding_right"
                android:layout_width="0.0dip"
                android:layout_height="36.0dip"
                android:minWidth="@dimen/abc_search_view_text_min_width"
                android:singleLine="true"
                android:layout_weight="1.0"
                android:inputType="textAutoComplete|textNoSuggestions"
                android:dropDownAnchor="@id/search_edit_frame"
                android:imeOptions="actionSearch"
                android:dropDownHeight="wrap_content"
                android:dropDownHorizontalOffset="0.0dip"
                android:dropDownVerticalOffset="0.0dip"
                class="android.support.v7.widget.SearchView$SearchAutoComplete" />
            <android.support.v7.internal.widget.TintImageView
                android:layout_gravity="center_vertical"
                android:id="@id/search_close_btn"
                android:background="?selectableItemBackgroundBorderless"
                android:paddingLeft="8.0dip"
                android:paddingRight="8.0dip"
                android:focusable="true"
                android:layout_width="wrap_content"
                android:layout_height="fill_parent"
                android:contentDescription="@string/abc_searchview_description_clear" />
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:id="@id/submit_area"
            android:layout_width="wrap_content"
            android:layout_height="fill_parent">
            <android.support.v7.internal.widget.TintImageView
                android:layout_gravity="center_vertical"
                android:id="@id/search_go_btn"
                android:background="?selectableItemBackgroundBorderless"
                android:paddingLeft="16.0dip"
                android:paddingRight="16.0dip"
                android:focusable="true"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="fill_parent"
                android:contentDescription="@string/abc_searchview_description_submit" />
            <android.support.v7.internal.widget.TintImageView
                android:layout_gravity="center_vertical"
                android:id="@id/search_voice_btn"
                android:background="?selectableItemBackgroundBorderless"
                android:paddingLeft="16.0dip"
                android:paddingRight="16.0dip"
                android:focusable="true"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="fill_parent"
                android:contentDescription="@string/abc_searchview_description_voice" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>