package ratmil.domino;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import ratmil.domino.model.DominoGame;
import ratmil.domino.model.Player;

/**
 * Main menu activity for the Domino game
 */
public class DominoMainActivity extends Activity {
    
    private Button newGameButton;
    private Button joinGameButton;
    private Button createGameButton;
    private Button demoModeButton;
    private Button settingsButton;
    private Button exitButton;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.domino_main_menu);
        
        initializeViews();
        setupClickListeners();
    }
    
    private void initializeViews() {
        newGameButton = findViewById(R.id.new_game_button);
        joinGameButton = findViewById(R.id.join_game_button);
        createGameButton = findViewById(R.id.create_game_button);
        demoModeButton = findViewById(R.id.demo_mode_button);
        settingsButton = findViewById(R.id.settings_button);
        exitButton = findViewById(R.id.exit_button);
    }
    
    private void setupClickListeners() {
        newGameButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startNewGame();
            }
        });
        
        joinGameButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                joinMultiplayerGame();
            }
        });
        
        createGameButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                createMultiplayerGame();
            }
        });
        
        demoModeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startDemoMode();
            }
        });
        
        settingsButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openSettings();
            }
        });
        
        exitButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                exitGame();
            }
        });
    }
    
    private void startNewGame() {
        Intent intent = new Intent(this, DominoGameSetupActivity.class);
        intent.putExtra("game_mode", DominoGame.GameMode.SINGLE_PLAYER.name());
        startActivity(intent);
    }
    
    private void joinMultiplayerGame() {
        Intent intent = new Intent(this, DominoConnectionActivity.class);
        intent.putExtra("connection_type", "join");
        startActivity(intent);
    }
    
    private void createMultiplayerGame() {
        Intent intent = new Intent(this, DominoConnectionActivity.class);
        intent.putExtra("connection_type", "create");
        startActivity(intent);
    }
    
    private void startDemoMode() {
        // Create a demo game with 4 AI players
        DominoGame demoGame = new DominoGame(DominoGame.GameMode.SINGLE_PLAYER);
        
        // Add AI players
        demoGame.addPlayer(new Player("Hal", Player.PlayerType.AI_HARD));
        demoGame.addPlayer(new Player("Shirka", Player.PlayerType.AI_MEDIUM));
        demoGame.addPlayer(new Player("Eddie", Player.PlayerType.AI_EASY));
        demoGame.addPlayer(new Player("Demo AI", Player.PlayerType.AI_MEDIUM));
        
        Intent intent = new Intent(this, DominoActivity.class);
        intent.putExtra("demo_mode", true);
        intent.putExtra("game_object", demoGame);
        startActivity(intent);
    }
    
    private void openSettings() {
        Intent intent = new Intent(this, DominoSettingsActivity.class);
        startActivity(intent);
    }
    
    private void exitGame() {
        finish();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // Update UI if needed
    }
    
    @Override
    public void onBackPressed() {
        exitGame();
    }
}
