<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="ratmil.domino">

    <!-- Bluetooth permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />

    <!-- Bluetooth permissions for Android 12+ -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"
        tools:targetApi="s" />

    <!-- Location permission for Bluetooth discovery -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- Bluetooth feature -->
    <uses-feature android:name="android.hardware.bluetooth" android:required="false" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:supportsRtl="true"
        tools:targetApi="31">

        <!-- Main Activity (Launcher) -->
        <activity
            android:name=".DominoMainActivity"
            android:label="@string/app_name"
            android:theme="@style/AppTheme"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Game Activity -->
        <activity
            android:name=".DominoActivity"
            android:label="@string/title_activity_domino"
            android:theme="@style/AppTheme"
            android:screenOrientation="landscape"
            android:exported="false" />

        <!-- Game Setup Activity -->
        <activity
            android:name=".DominoGameSetupActivity"
            android:label="@string/game_setup"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <!-- Settings Activity -->
        <activity
            android:name=".DominoSettingsActivity"
            android:label="@string/settings"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <!-- Connection Activity -->
        <activity
            android:name=".DominoConnectionActivity"
            android:label="@string/title_activity_connection"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <!-- Debug Activity -->
        <activity
            android:name=".DebugActivity"
            android:label="@string/title_activity_debug"
            android:theme="@style/AppTheme"
            android:exported="false" />

        <!-- Game Connection Service -->
        <service
            android:name=".GameConnectionService"
            android:label="@string/network"
            android:exported="false" />

    </application>
</manifest>