<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="abc_alert_dialog_material" modulePackage="ratmil.domino" filePath="app\src\main\res\layout-v17\abc_alert_dialog_material.xml" directory="layout-v17" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@id/parentPanel"><Targets><Target id="@id/parentPanel" tag="layout-v17/abc_alert_dialog_material_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="113" endOffset="14"/></Target><Target id="@id/topPanel" view="LinearLayout"><Expressions/><location startLine="7" startOffset="4" endLine="37" endOffset="18"/></Target><Target id="@id/title_template" view="LinearLayout"><Expressions/><location startLine="12" startOffset="8" endLine="36" endOffset="22"/></Target><Target id="@android:id/icon" view="ImageView"><Expressions/><location startLine="21" startOffset="12" endLine="27" endOffset="51"/></Target><Target id="@id/alertTitle" view="android.support.v7.internal.widget.DialogTitle"><Expressions/><location startLine="28" startOffset="12" endLine="35" endOffset="51"/></Target><Target id="@id/contentPanel" view="FrameLayout"><Expressions/><location startLine="38" startOffset="4" endLine="68" endOffset="17"/></Target><Target id="@id/scrollView" view="ScrollView"><Expressions/><location startLine="44" startOffset="8" endLine="67" endOffset="20"/></Target><Target id="@android:id/message" view="TextView"><Expressions/><location startLine="53" startOffset="16" endLine="60" endOffset="69"/></Target><Target id="@id/textSpacerNoButtons" view="View"><Expressions/><location startLine="61" startOffset="16" endLine="65" endOffset="84"/></Target><Target id="@id/customPanel" view="FrameLayout"><Expressions/><location startLine="69" startOffset="4" endLine="79" endOffset="17"/></Target><Target id="@id/custom" view="FrameLayout"><Expressions/><location startLine="75" startOffset="8" endLine="78" endOffset="50"/></Target><Target id="@id/buttonPanel" view="LinearLayout"><Expressions/><location startLine="80" startOffset="4" endLine="112" endOffset="18"/></Target><Target id="@android:id/button3" view="Button"><Expressions/><location startLine="92" startOffset="8" endLine="96" endOffset="50"/></Target><Target id="@android:id/button2" view="Button"><Expressions/><location startLine="102" startOffset="8" endLine="106" endOffset="51"/></Target><Target id="@android:id/button1" view="Button"><Expressions/><location startLine="107" startOffset="8" endLine="111" endOffset="51"/></Target></Targets></Layout>