<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/domino_background"
    android:padding="24dp"
    android:gravity="center">

    <!-- Game Logo/Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/cuban_domino"
        style="@style/DominoText.Title"
        android:background="@drawable/domino_card_background"
        android:layout_marginBottom="32dp" />

    <!-- Connection Status -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/domino_card_background"
        android:padding="24dp"
        android:layout_marginBottom="32dp"
        android:gravity="center">

        <TextView
            android:id="@+id/connection_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/getting_game_data"
            style="@style/DominoText"
            android:textSize="18sp"
            android:layout_marginBottom="16dp" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/device_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            style="@style/DominoText"
            android:textStyle="bold" />
    </LinearLayout>

    <!-- Player List -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/domino_card_background"
        android:padding="16dp"
        android:layout_marginBottom="32dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/players"
            style="@style/DominoText.Title"
            android:layout_marginBottom="16dp" />

        <LinearLayout
            android:id="@+id/players_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/waiting_for_players"
                style="@style/DominoText"
                android:gravity="center"
                android:padding="16dp" />
        </LinearLayout>
    </LinearLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/start_game_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/start_game"
            style="@style/DominoButton.Positive"
            android:layout_marginEnd="8dp"
            android:visibility="gone" />

        <Button
            android:id="@+id/cancel_connection_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/cancel"
            style="@style/DominoButton.Negative"
            android:layout_marginStart="8dp" />
    </LinearLayout>
</LinearLayout>
