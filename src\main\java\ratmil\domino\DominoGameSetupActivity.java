package ratmil.domino;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Switch;

import ratmil.domino.model.DominoGame;
import ratmil.domino.model.Player;

/**
 * Activity for setting up a new game
 */
public class DominoGameSetupActivity extends Activity {
    
    private EditText playerNameEdit;
    private Spinner maxScoreSpinner;
    private Spinner dominoSetSpinner;
    private Spinner difficultySpinner;
    private Switch teamPlaySwitch;
    private Button startGameButton;
    private Button cancelButton;
    
    private DominoGame.GameMode gameMode;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.domino_game_setup);
        
        gameMode = DominoGame.GameMode.valueOf(getIntent().getStringExtra("game_mode"));
        
        initializeViews();
        setupSpinners();
        setupClickListeners();
    }
    
    private void initializeViews() {
        playerNameEdit = findViewById(R.id.player_name_edit);
        maxScoreSpinner = findViewById(R.id.max_score_spinner);
        dominoSetSpinner = findViewById(R.id.domino_set_spinner);
        difficultySpinner = findViewById(R.id.difficulty_spinner);
        teamPlaySwitch = findViewById(R.id.team_play_switch);
        startGameButton = findViewById(R.id.start_game_button);
        cancelButton = findViewById(R.id.cancel_button);
        
        // Set default player name
        playerNameEdit.setText(getString(R.string.me));
    }
    
    private void setupSpinners() {
        // Max Score options
        String[] maxScoreOptions = {"50", "100", "150", "200", "300", "500"};
        ArrayAdapter<String> maxScoreAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, maxScoreOptions);
        maxScoreAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        maxScoreSpinner.setAdapter(maxScoreAdapter);
        maxScoreSpinner.setSelection(1); // Default to 100
        
        // Domino Set options
        String[] dominoSetOptions = {getString(R.string.pieces_28), getString(R.string.pieces_55)};
        ArrayAdapter<String> dominoSetAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, dominoSetOptions);
        dominoSetAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        dominoSetSpinner.setAdapter(dominoSetAdapter);
        
        // Difficulty options
        String[] difficultyOptions = {"Easy", "Medium", "Hard", "Mixed"};
        ArrayAdapter<String> difficultyAdapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_spinner_item, difficultyOptions);
        difficultyAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        difficultySpinner.setAdapter(difficultyAdapter);
        difficultySpinner.setSelection(2); // Default to Hard
    }
    
    private void setupClickListeners() {
        startGameButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startGame();
            }
        });
        
        cancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }
    
    private void startGame() {
        // Create game with selected settings
        DominoGame game = new DominoGame(gameMode);
        
        // Configure game settings
        String maxScoreStr = (String) maxScoreSpinner.getSelectedItem();
        game.setMaxScore(Integer.parseInt(maxScoreStr));
        
        boolean useDoubleNine = dominoSetSpinner.getSelectedItemPosition() == 1;
        game.setTeamPlay(teamPlaySwitch.isChecked());
        
        // Add human player
        String playerName = playerNameEdit.getText().toString().trim();
        if (playerName.isEmpty()) {
            playerName = getString(R.string.me);
        }
        game.addPlayer(new Player(playerName, Player.PlayerType.HUMAN));
        
        // Add AI players based on difficulty
        String difficulty = (String) difficultySpinner.getSelectedItem();
        addAIPlayers(game, difficulty);
        
        // Start the game activity
        Intent intent = new Intent(this, DominoActivity.class);
        intent.putExtra("game_object", game);
        intent.putExtra("game_mode", gameMode.name());
        startActivity(intent);
        finish();
    }
    
    private void addAIPlayers(DominoGame game, String difficulty) {
        switch (difficulty) {
            case "Easy":
                game.addPlayer(new Player(getString(R.string.ai1), Player.PlayerType.AI_EASY));
                game.addPlayer(new Player(getString(R.string.ai2), Player.PlayerType.AI_EASY));
                game.addPlayer(new Player(getString(R.string.ai3), Player.PlayerType.AI_EASY));
                break;
            case "Medium":
                game.addPlayer(new Player(getString(R.string.ai1), Player.PlayerType.AI_MEDIUM));
                game.addPlayer(new Player(getString(R.string.ai2), Player.PlayerType.AI_MEDIUM));
                game.addPlayer(new Player(getString(R.string.ai3), Player.PlayerType.AI_MEDIUM));
                break;
            case "Hard":
                game.addPlayer(new Player(getString(R.string.ai1), Player.PlayerType.AI_HARD));
                game.addPlayer(new Player(getString(R.string.ai2), Player.PlayerType.AI_HARD));
                game.addPlayer(new Player(getString(R.string.ai3), Player.PlayerType.AI_HARD));
                break;
            case "Mixed":
                game.addPlayer(new Player(getString(R.string.ai1), Player.PlayerType.AI_HARD));
                game.addPlayer(new Player(getString(R.string.ai2), Player.PlayerType.AI_MEDIUM));
                game.addPlayer(new Player(getString(R.string.ai3), Player.PlayerType.AI_EASY));
                break;
        }
    }
}
